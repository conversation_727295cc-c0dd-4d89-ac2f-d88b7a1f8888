# utils/population_coding_viz.py
"""
图 4c: 智能体间距离的群体编码涌现 (Emergence of a Population Code for Inter-Agent Distance)
展示神经元群体通过多样化的调谐曲线来共同编码距离
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from tqdm import tqdm
from scipy.ndimage import gaussian_filter
from scipy.stats import pearsonr
from scipy.interpolate import interp1d
from sklearn.preprocessing import MinMaxScaler
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional
import time
import seaborn as sns
from scipy.ndimage import gaussian_filter1d

# 导入项目模块
import sys
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble
from utils.functional_gating_viz import TrajectoryGenerator, ProximityData

# Nature子刊风格配置
plt.style.use('default')
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans'],
    'font.size': 10,
    'axes.labelsize': 11,
    'axes.titlesize': 12,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 8,
    'figure.titlesize': 14,
    'figure.dpi': 150,
    'axes.linewidth': 1.0,
    'xtick.major.width': 1.0,
    'ytick.major.width': 1.0,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.edgecolor': 'black',
    'axes.linewidth': 1.2,
})

# Nature风格配色方案
NATURE_COLORS = {
    'close': '#E31A1C',      # 红色 - 近距离
    'mid': '#1F78B4',        # 蓝色 - 中距离  
    'far': '#33A02C',        # 绿色 - 远距离
    'population': '#FF7F00', # 橙色 - 群体
    'background': '#F0F0F0', # 浅灰色背景
    'grid': '#CCCCCC',       # 网格颜色
}

@dataclass
class DistanceCell:
    """距离细胞数据结构 - 更新版本"""
    neuron_idx: int
    category: str  # 'close', 'mid', 'far'
    peak_distance: float
    max_activation: float
    tuning_curve: np.ndarray
    distances: np.ndarray
    activations: np.ndarray
    relative_positions: np.ndarray
    sparsity: float  # 替代r_squared
    peak_width: float
    peak_significance: float  # 新增
    selectivity_index: float  # 新增

class DistanceCellClassifier:
    """距离细胞分类器 - 基于稀疏度版本"""
    
    def __init__(self, close_threshold: float = 5.0, far_threshold: float = 12.0):
        self.close_threshold = close_threshold
        self.far_threshold = far_threshold
    
    def classify_neurons(self, model: SocialGridCellNetwork, config: Config,
                        device: torch.device, num_reps: int = 400, max_distance: float = 8.0) -> Dict[str, List[DistanceCell]]:
        """分类所有神经元为近距离/中距离/远距离细胞"""
        
        trajectory_gen = TrajectoryGenerator(config)
        distance_cells = {'close': [], 'mid': [], 'far': []}
        
        print("Analyzing all neurons for distance tuning using sparsity metrics...")
        
        model.eval()
        with torch.no_grad():
            # 生成专门的邻近度分析轨迹
            trajs = trajectory_gen.get_proximity_analysis_trajectories(num_reps=num_reps, max_distance=max_distance)
            
            # 准备输入数据
            self_vel_input = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(), 
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)
            
            peer_vel_input = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(), 
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

            # 前向传播
            outputs = model(self_vel_input, self_init_pos, self_init_hd, peer_vel_input, peer_init_pos, peer_init_hd)
            relational_activations = outputs['relational_activations'].cpu().numpy()  # (batch, seq, 64)
            
            # 计算距离和相对位置
            self_pos = trajs['self_pos']
            peer_pos = trajs['peer_pos']
            distances = np.linalg.norm(self_pos - peer_pos, axis=-1).flatten()
            relative_positions = (peer_pos - self_pos).reshape(-1, 2)
            
            # 分析每个神经元
            for neuron_idx in tqdm(range(relational_activations.shape[-1]), desc="Classifying neurons"):
                activations = relational_activations[:, :, neuron_idx].flatten()
                
                # 跳过静默神经元
                if np.std(activations) < 1e-8:
                    continue
                
                # 计算调谐曲线
                tuning_curve, distance_bins = self._compute_tuning_curve(distances, activations)
                
                # 找到峰值距离
                peak_idx = np.argmax(tuning_curve)
                peak_distance = distance_bins[peak_idx]
                max_activation = tuning_curve[peak_idx]
                
                # 计算稀疏度和其他指标
                sparsity = self._compute_sparsity(tuning_curve)
                peak_significance = self._compute_peak_significance(tuning_curve)
                selectivity_index = self._compute_selectivity_index(distances, activations)
                
                # 计算峰值宽度（半高全宽）
                peak_width = self._compute_peak_width(tuning_curve, distance_bins)
                
                # 使用稀疏度和其他指标判断是否为距离细胞
                is_distance_cell = (
                    sparsity > 0.3 and  # 高稀疏度：神经元在大部分距离上沉默
                    peak_significance > 2.0 and  # 显著的峰值
                    selectivity_index > 0.2 and  # 足够的选择性
                    max_activation > np.mean(activations) + 0.5 * np.std(activations)  # 峰值足够高
                )
                
                if is_distance_cell:
                    # 分类
                    if peak_distance <= self.close_threshold:
                        category = 'close'
                    elif peak_distance >= self.far_threshold:
                        category = 'far'
                    else:
                        category = 'mid'
                    
                    distance_cell = DistanceCell(
                        neuron_idx=neuron_idx,
                        category=category,
                        peak_distance=peak_distance,
                        max_activation=max_activation,
                        tuning_curve=tuning_curve,
                        distances=distance_bins,
                        activations=activations,
                        relative_positions=relative_positions,
                        sparsity=sparsity,
                        peak_width=peak_width,
                        peak_significance=peak_significance,
                        selectivity_index=selectivity_index
                    )
                    
                    distance_cells[category].append(distance_cell)
        
        # 按峰值距离排序
        for category in distance_cells:
            distance_cells[category].sort(key=lambda x: x.peak_distance)
        
        print(f"Classification results:")
        print(f"  Close-distance cells: {len(distance_cells['close'])}")
        print(f"  Mid-distance cells: {len(distance_cells['mid'])}")
        print(f"  Far-distance cells: {len(distance_cells['far'])}")
        print(f"  Total distance cells: {sum(len(distance_cells[cat]) for cat in distance_cells)}")
        
        return distance_cells
    
    def _compute_tuning_curve(self, distances: np.ndarray, activations: np.ndarray,
                            n_bins: int = 30) -> Tuple[np.ndarray, np.ndarray]:
        """计算距离调谐曲线"""
        max_dist = min(np.max(distances), 8.0)
        bin_edges = np.linspace(0, max_dist, n_bins + 1)
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
        
        tuning_curve = np.zeros(n_bins)
        for i in range(n_bins):
            mask = (distances >= bin_edges[i]) & (distances < bin_edges[i + 1])
            if np.sum(mask) > 0:
                tuning_curve[i] = np.mean(activations[mask])
        
        return tuning_curve, bin_centers
    
    def _compute_sparsity(self, tuning_curve: np.ndarray) -> float:
        """
        计算稀疏度指标
        使用公式: Sparsity = (1 - (ΣP_i*R_i)^2 / Σ(P_i*R_i^2)) / (1 - 1/N)
        
        其中:
        - P_i: 在第i个距离bin的停留概率（简化为1/N）
        - R_i: 在第i个距离bin的平均激活率
        - N: 总的bin数量
        """
        N = len(tuning_curve)
        if N <= 1:
            return 0.0
        
        # 归一化激活率，避免负值
        R = np.maximum(tuning_curve, 0)
        
        # 简化假设：每个bin的停留概率相等 P_i = 1/N
        P = np.ones(N) / N
        
        # 计算分子和分母
        numerator = (np.sum(P * R)) ** 2
        denominator = np.sum(P * R**2)
        
        if denominator == 0:
            return 0.0
        
        # 计算稀疏度
        sparsity = (1 - numerator / denominator) / (1 - 1/N)
        
        # 确保稀疏度在[0, 1]范围内
        return np.clip(sparsity, 0.0, 1.0)
    
    def _compute_peak_significance(self, tuning_curve: np.ndarray) -> float:
        """计算峰值显著性：峰值相对于基线的显著程度"""
        if len(tuning_curve) == 0:
            return 0.0
        
        peak_val = np.max(tuning_curve)
        baseline = np.percentile(tuning_curve, 25)  # 用25%分位数作为基线
        curve_std = np.std(tuning_curve)
        
        if curve_std == 0:
            return 0.0
        
        peak_significance = (peak_val - baseline) / curve_std
        return max(0.0, peak_significance)
    
    def _compute_selectivity_index(self, distances: np.ndarray, activations: np.ndarray) -> float:
        """计算选择性指数：不同距离区间激活差异的标准化度量"""
        if len(distances) == 0 or len(activations) == 0:
            return 0.0
        
        # 分为三个距离区间
        close_mask = distances <= 3.0
        mid_mask = (distances > 3.0) & (distances <= 5.0)
        far_mask = distances > 5.0
        
        close_act = np.mean(activations[close_mask]) if np.sum(close_mask) > 0 else 0
        mid_act = np.mean(activations[mid_mask]) if np.sum(mid_mask) > 0 else 0
        far_act = np.mean(activations[far_mask]) if np.sum(far_mask) > 0 else 0
        
        region_activations = [close_act, mid_act, far_act]
        mean_activation = np.mean(region_activations)
        
        if mean_activation == 0:
            return 0.0
        
        selectivity_index = np.std(region_activations) / mean_activation
        return selectivity_index
    
    def _compute_peak_width(self, tuning_curve: np.ndarray, distance_bins: np.ndarray) -> float:
        """计算峰值宽度（半高全宽）"""
        if len(tuning_curve) == 0:
            return 0.0
        
        peak_val = np.max(tuning_curve)
        half_max = peak_val / 2
        
        # 找到半高点
        above_half = tuning_curve >= half_max
        if np.sum(above_half) < 2:
            return 0.0
        
        indices = np.where(above_half)[0]
        width = distance_bins[indices[-1]] - distance_bins[indices[0]]
        return width

def create_2d_activation_heatmap(distance_cell: DistanceCell, max_distance: float = 8.0,
                               grid_size: int = 60) -> np.ndarray:
    """创建2D激活热图 - 修复版本"""
    heatmap = np.zeros((grid_size, grid_size))
    counts = np.zeros((grid_size, grid_size))

    # 打印调试信息
    print(f"Creating heatmap for neuron {distance_cell.neuron_idx}")
    print(f"Relative positions range: X[{np.min(distance_cell.relative_positions[:, 0]):.2f}, {np.max(distance_cell.relative_positions[:, 0]):.2f}], Y[{np.min(distance_cell.relative_positions[:, 1]):.2f}, {np.max(distance_cell.relative_positions[:, 1]):.2f}]")
    print(f"Activations range: [{np.min(distance_cell.activations):.3f}, {np.max(distance_cell.activations):.3f}]")

    # 将相对位置映射到网格坐标
    for i in range(len(distance_cell.relative_positions)):
        rel_pos = distance_cell.relative_positions[i]
        act = distance_cell.activations[i]

        # 将相对位置转换为网格坐标（中心为观察者位置）
        # 确保坐标范围在 [-max_distance, max_distance]
        if abs(rel_pos[0]) <= max_distance and abs(rel_pos[1]) <= max_distance:
            grid_x = int(np.clip((rel_pos[0] + max_distance) / (2 * max_distance) * grid_size, 0, grid_size - 1))
            grid_y = int(np.clip((rel_pos[1] + max_distance) / (2 * max_distance) * grid_size, 0, grid_size - 1))

            heatmap[grid_y, grid_x] += act
            counts[grid_y, grid_x] += 1

    # 计算平均激活
    with np.errstate(divide='ignore', invalid='ignore'):
        avg_heatmap = np.where(counts > 0, heatmap / counts, 0)

    print(f"Heatmap data range: [{np.min(avg_heatmap):.3f}, {np.max(avg_heatmap):.3f}]")
    print(f"Non-zero pixels: {np.sum(avg_heatmap > 0)}/{grid_size*grid_size}")

    # 高斯平滑
    return gaussian_filter(avg_heatmap, sigma=1.2)


def create_2d_activation_heatmap(distance_cell: DistanceCell, max_distance: float = 8.0,
                               grid_size: int = 60) -> np.ndarray:
    """创建2D激活热图 - 修复版本"""
    heatmap = np.zeros((grid_size, grid_size))
    counts = np.zeros((grid_size, grid_size))

    # 打印调试信息
    print(f"Creating heatmap for neuron {distance_cell.neuron_idx}")
    print(f"Relative positions range: X[{np.min(distance_cell.relative_positions[:, 0]):.2f}, {np.max(distance_cell.relative_positions[:, 0]):.2f}], Y[{np.min(distance_cell.relative_positions[:, 1]):.2f}, {np.max(distance_cell.relative_positions[:, 1]):.2f}]")
    print(f"Activations range: [{np.min(distance_cell.activations):.3f}, {np.max(distance_cell.activations):.3f}]")

    # 将相对位置映射到网格坐标
    for i in range(len(distance_cell.relative_positions)):
        rel_pos = distance_cell.relative_positions[i]
        act = distance_cell.activations[i]

        # 将相对位置转换为网格坐标（中心为观察者位置）
        # 确保坐标范围在 [-max_distance, max_distance]
        if abs(rel_pos[0]) <= max_distance and abs(rel_pos[1]) <= max_distance:
            grid_x = int(np.clip((rel_pos[0] + max_distance) / (2 * max_distance) * grid_size, 0, grid_size - 1))
            grid_y = int(np.clip((rel_pos[1] + max_distance) / (2 * max_distance) * grid_size, 0, grid_size - 1))

            heatmap[grid_y, grid_x] += act
            counts[grid_y, grid_x] += 1

    # 计算平均激活
    with np.errstate(divide='ignore', invalid='ignore'):
        avg_heatmap = np.where(counts > 0, heatmap / counts, 0)

    print(f"Heatmap data range: [{np.min(avg_heatmap):.3f}, {np.max(avg_heatmap):.3f}]")
    print(f"Non-zero pixels: {np.sum(avg_heatmap > 0)}/{grid_size*grid_size}")

    # 高斯平滑
    return gaussian_filter(avg_heatmap, sigma=1.2)


def plot_individual_distance_cell(distance_cell: DistanceCell, output_dir: str,
                                max_distance: float = 8.0, close_threshold: float = 3.0, 
                                far_threshold: float = 5.0):
    """绘制单个距离细胞的2D热图和1D调谐曲线 - 稀疏度版本"""

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    # 左侧：2D激活热图
    heatmap = create_2d_activation_heatmap(distance_cell, max_distance)
    
    # 确保extent正确设置为 [-max_distance, max_distance]
    extent = [-max_distance, max_distance, -max_distance, max_distance]

    # 检查热力图是否有数据
    if np.max(heatmap) == 0:
        print(f"Warning: No data in heatmap for neuron {distance_cell.neuron_idx}")
        # 创建示例数据用于调试
        y, x = np.ogrid[-max_distance:max_distance:60j, -max_distance:max_distance:60j]
        distances_2d = np.sqrt(x**2 + y**2)
        heatmap = np.exp(-(distances_2d - distance_cell.peak_distance)**2 / (2 * (distance_cell.peak_width/2)**2))

    im = ax1.imshow(heatmap, cmap='jet', origin='lower', extent=extent,
                    aspect='equal', interpolation='bilinear', vmin=0)

    # 标记观察者位置
    ax1.scatter(0, 0, color='cyan', s=120, marker='*', edgecolors='black',
               linewidth=2, label='Observer', zorder=5)

    # 添加距离圆圈
    circle_params = [
        (close_threshold, 'white', '--', f'{close_threshold}m'),
        (far_threshold, 'white', '--', f'{far_threshold}m'),
        (max_distance, 'white', '-', f'{max_distance}m')
    ]
    
    for radius, color, linestyle, label in circle_params:
        circle = plt.Circle((0, 0), radius, fill=False, color=color,
                          linestyle=linestyle, alpha=0.7, linewidth=1.5)
        ax1.add_patch(circle)
        # 将标签放在45度角位置
        angle_rad = np.pi / 4
        x_pos = radius * np.cos(angle_rad)
        y_pos = radius * np.sin(angle_rad)
        ax1.text(x_pos, y_pos, label, color=color,
                fontsize=9, ha='center', va='center', weight='bold',
                bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7))

    ax1.set_xlabel('Relative X Position (m)', fontweight='bold')
    ax1.set_ylabel('Relative Y Position (m)', fontweight='bold')
    ax1.set_title(f'{distance_cell.category.title()}-Distance Cell (Neuron {distance_cell.neuron_idx})\n2D Activation Map',
                 fontweight='bold', pad=15)
    ax1.legend(loc='upper right', frameon=True, fancybox=False, shadow=False)
    ax1.grid(True, linestyle=':', alpha=0.3, color='white')
    
    # 确保坐标轴范围正确
    ax1.set_xlim(-max_distance, max_distance)
    ax1.set_ylim(-max_distance, max_distance)

    # 添加colorbar
    cbar = plt.colorbar(im, ax=ax1, shrink=0.8)
    cbar.set_label('Neural Activation', rotation=270, labelpad=15, fontweight='bold')

    # 右侧：1D距离调谐曲线
    color = NATURE_COLORS[distance_cell.category]

    ax2.plot(distance_cell.distances, distance_cell.tuning_curve,
             color=color, linewidth=3, alpha=0.8, label='Tuning Curve')
    ax2.fill_between(distance_cell.distances, distance_cell.tuning_curve,
                     alpha=0.3, color=color)

    # 标记峰值
    peak_idx = np.argmax(distance_cell.tuning_curve)
    ax2.scatter(distance_cell.peak_distance, distance_cell.max_activation,
               color=color, s=100, zorder=5, edgecolors='black', linewidth=2)
    ax2.axvline(distance_cell.peak_distance, color=color, linestyle='--', alpha=0.7)

    # 添加阈值线
    ax2.axvline(close_threshold, color='red', linestyle=':', alpha=0.7, label=f'Close threshold ({close_threshold}m)')
    ax2.axvline(far_threshold, color='green', linestyle=':', alpha=0.7, label=f'Far threshold ({far_threshold}m)')

    ax2.set_xlabel('Inter-Agent Distance (m)', fontweight='bold')
    ax2.set_ylabel('Neural Activation', fontweight='bold')
    ax2.set_title(f'Distance Tuning Curve\nPeak: {distance_cell.peak_distance:.1f}m, Sparsity: {distance_cell.sparsity:.3f}',
                 fontweight='bold', pad=15)
    ax2.grid(True, linestyle=':', alpha=0.5)
    ax2.set_xlim(0, max_distance)
    ax2.set_ylim(bottom=0)
    ax2.legend(fontsize=8)

    # 添加基于稀疏度的统计信息
    stats_text = (f'Peak Distance: {distance_cell.peak_distance:.1f}m\n'
                 f'Max Activation: {distance_cell.max_activation:.3f}\n'
                 f'Sparsity: {distance_cell.sparsity:.3f}\n'
                 f'Peak Significance: {distance_cell.peak_significance:.2f}\n'
                 f'Selectivity Index: {distance_cell.selectivity_index:.3f}\n'
                 f'Peak Width: {distance_cell.peak_width:.1f}m')
    
    ax2.text(0.95, 0.95, stats_text, transform=ax2.transAxes, fontsize=8,
            verticalalignment='top', horizontalalignment='right',
            bbox=dict(boxstyle='round,pad=0.4', facecolor='white', alpha=0.9, edgecolor='gray'))

    plt.tight_layout()

    # 保存图形
    category_dir = os.path.join(output_dir, f"{distance_cell.category}_distance_cells")
    os.makedirs(category_dir, exist_ok=True)

    filename = f"distance_cell_neuron_{distance_cell.neuron_idx}_{distance_cell.category}.svg"
    save_path = os.path.join(category_dir, filename)

    plt.savefig(save_path, format='svg', bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close(fig)

    return save_path

def create_combined_figure(distance_cells: Dict[str, List[DistanceCell]],
                          output_dir: str, max_distance: float = 8.0,
                          close_threshold: float = 3.0, far_threshold: float = 5.0):
    """创建2x2组合图展示距离编码的涌现 - 稀疏度版本"""

    fig = plt.figure(figsize=(16, 12))

    # 选择每个类别的最佳代表（按稀疏度排序）
    representatives = {}
    for category in ['close', 'mid', 'far']:
        if distance_cells[category]:
            # 选择稀疏度最高的神经元作为代表
            best_cell = max(distance_cells[category], key=lambda x: x.sparsity)
            representatives[category] = best_cell

    extent = [-max_distance, max_distance, -max_distance, max_distance]

    # 面板 (i): 近距离细胞
    if 'close' in representatives:
        ax1 = plt.subplot(2, 2, 1)
        cell = representatives['close']
        heatmap = create_2d_activation_heatmap(cell, max_distance)

        im1 = ax1.imshow(heatmap, cmap='jet', origin='lower', extent=extent,
                        aspect='equal', interpolation='bilinear', vmin=0)
        ax1.scatter(0, 0, color='cyan', s=80, marker='*', edgecolors='black', linewidth=1.5)

        # 添加距离圆圈
        for radius, linestyle in [(close_threshold, '--'), (far_threshold, '--'), (max_distance, '-')]:
            circle = plt.Circle((0, 0), radius, fill=False, color='white',
                              linestyle=linestyle, alpha=0.6, linewidth=1)
            ax1.add_patch(circle)
            ax1.text(radius*0.7, radius*0.7, f'{radius}m', color='white',
                    fontsize=8, ha='center', va='center', weight='bold')

        ax1.set_title(f'(i) Close-Distance Cell\nNeuron {cell.neuron_idx}, Peak: {cell.peak_distance:.1f}m',
                     fontweight='bold', fontsize=11)
        ax1.set_xlabel('Relative X (m)', fontweight='bold')
        ax1.set_ylabel('Relative Y (m)', fontweight='bold')
        ax1.set_xlim(-max_distance, max_distance)
        ax1.set_ylim(-max_distance, max_distance)

        # 面板 (ii): 近距离细胞的调谐曲线
        ax2 = plt.subplot(2, 2, 2)
        ax2.plot(cell.distances, cell.tuning_curve, color=NATURE_COLORS['close'],
                linewidth=3, alpha=0.8)
        ax2.fill_between(cell.distances, cell.tuning_curve, alpha=0.3, color=NATURE_COLORS['close'])
        ax2.axvline(cell.peak_distance, color=NATURE_COLORS['close'], linestyle='--', alpha=0.7)
        
        # 添加阈值线
        ax2.axvline(close_threshold, color='red', linestyle=':', alpha=0.7)
        ax2.axvline(far_threshold, color='green', linestyle=':', alpha=0.7)

        ax2.set_title(f'(ii) Close-Distance Tuning\nSparsity = {cell.sparsity:.3f}',
                     fontweight='bold', fontsize=11)
        ax2.set_xlabel('Distance (m)', fontweight='bold')
        ax2.set_ylabel('Activation', fontweight='bold')
        ax2.grid(True, alpha=0.3)
        ax2.set_xlim(0, max_distance)

    # 面板 (iii): 中距离细胞
    if 'mid' in representatives:
        ax3 = plt.subplot(2, 2, 3)
        cell = representatives['mid']
        heatmap = create_2d_activation_heatmap(cell, max_distance)

        im3 = ax3.imshow(heatmap, cmap='jet', origin='lower', extent=extent,
                        aspect='equal', interpolation='bilinear', vmin=0)
        ax3.scatter(0, 0, color='cyan', s=80, marker='*', edgecolors='black', linewidth=1.5)

        for radius, linestyle in [(close_threshold, '--'), (far_threshold, '--'), (max_distance, '-')]:
            circle = plt.Circle((0, 0), radius, fill=False, color='white',
                              linestyle=linestyle, alpha=0.6, linewidth=1)
            ax3.add_patch(circle)
            ax3.text(radius*0.7, radius*0.7, f'{radius}m', color='white',
                    fontsize=8, ha='center', va='center', weight='bold')

        ax3.set_title(f'(iii) Mid-Distance Cell\nNeuron {cell.neuron_idx}, Peak: {cell.peak_distance:.1f}m',
                     fontweight='bold', fontsize=11)
        ax3.set_xlabel('Relative X (m)', fontweight='bold')
        ax3.set_ylabel('Relative Y (m)', fontweight='bold')
        ax3.set_xlim(-max_distance, max_distance)
        ax3.set_ylim(-max_distance, max_distance)

    # 面板 (iv): 群体编码瓦片式覆盖
    ax4 = plt.subplot(2, 2, 4)

    # 收集所有距离细胞
    all_cells = []
    for category in ['close', 'mid', 'far']:
        all_cells.extend(distance_cells[category])
    all_cells.sort(key=lambda x: x.peak_distance)

    # 绘制所有调谐曲线
    for cell in all_cells:
        normalized_curve = cell.tuning_curve / np.max(cell.tuning_curve) if np.max(cell.tuning_curve) > 0 else cell.tuning_curve
        color = NATURE_COLORS[cell.category]
        # 线条透明度与稀疏度成正比，稀疏度越高越明显
        alpha = min(0.9, 0.3 + 0.6 * cell.sparsity)
        ax4.plot(cell.distances, normalized_curve, color=color, alpha=alpha, linewidth=1.5)

    # 添加阈值线
    ax4.axvline(close_threshold, color='red', linestyle=':', alpha=0.7, label=f'Close threshold ({close_threshold}m)')
    ax4.axvline(far_threshold, color='green', linestyle=':', alpha=0.7, label=f'Far threshold ({far_threshold}m)')

    # 添加图例
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color=NATURE_COLORS['close'], lw=2, label=f'Close (n={len(distance_cells["close"])})'),
        Line2D([0], [0], color=NATURE_COLORS['mid'], lw=2, label=f'Mid (n={len(distance_cells["mid"])})'),
        Line2D([0], [0], color=NATURE_COLORS['far'], lw=2, label=f'Far (n={len(distance_cells["far"])})'),
        Line2D([0], [0], color='red', linestyle=':', label=f'Close threshold'),
        Line2D([0], [0], color='green', linestyle=':', label=f'Far threshold')
    ]
    ax4.legend(handles=legend_elements, loc='upper right', fontsize=8)

    ax4.set_title('(iv) Population Code Tiling\nDistance Space Coverage (Line opacity ∝ Sparsity)',
                 fontweight='bold', fontsize=11)
    ax4.set_xlabel('Distance (m)', fontweight='bold')
    ax4.set_ylabel('Normalized Activation', fontweight='bold')
    ax4.grid(True, alpha=0.3)
    ax4.set_xlim(0, max_distance)
    ax4.set_ylim(0, 1.1)

    # 设置整体标题
    fig.suptitle('Emergence of a Population Code for Inter-Agent Distance\n(Based on Sparsity Analysis)',
                fontsize=18, fontweight='bold', y=0.95)

    plt.tight_layout()
    plt.subplots_adjust(top=0.88, hspace=0.3, wspace=0.3)

    # 保存图形
    filename = "population_distance_coding_emergence_sparsity.svg"
    save_path = os.path.join(output_dir, filename)

    plt.savefig(save_path, format='svg', bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close(fig)

    print(f"Combined figure (sparsity-based) saved to: {save_path}")
    return save_path

def print_sparsity_analysis_summary(distance_cells: Dict[str, List[DistanceCell]]):
    """打印稀疏度分析总结"""
    print("\n" + "="*60)
    print("SPARSITY-BASED DISTANCE CELL ANALYSIS SUMMARY")
    print("="*60)
    
    all_cells = []
    for category in ['close', 'mid', 'far']:
        all_cells.extend(distance_cells[category])
    
    if not all_cells:
        print("No distance cells found.")
        return
    
    # 总体统计
    total_cells = len(all_cells)
    avg_sparsity = np.mean([cell.sparsity for cell in all_cells])
    avg_peak_sig = np.mean([cell.peak_significance for cell in all_cells])
    avg_selectivity = np.mean([cell.selectivity_index for cell in all_cells])
    
    print(f"Total Distance Cells Found: {total_cells}")
    print(f"Average Sparsity: {avg_sparsity:.3f}")
    print(f"Average Peak Significance: {avg_peak_sig:.2f}")
    print(f"Average Selectivity Index: {avg_selectivity:.3f}")
    print()
    
    # 按类别统计
    for category in ['close', 'mid', 'far']:
        cells = distance_cells[category]
        if cells:
            print(f"{category.upper()}-DISTANCE CELLS (n={len(cells)}):")
            print(f"  Peak Distance Range: {min(cell.peak_distance for cell in cells):.1f} - {max(cell.peak_distance for cell in cells):.1f}m")
            print(f"  Average Sparsity: {np.mean([cell.sparsity for cell in cells]):.3f}")
            print(f"  Average Peak Significance: {np.mean([cell.peak_significance for cell in cells]):.2f}")
            print(f"  Average Selectivity: {np.mean([cell.selectivity_index for cell in cells]):.3f}")
            
            # 找出最稀疏的细胞
            best_cell = max(cells, key=lambda x: x.sparsity)
            print(f"  Most Sparse Cell: Neuron {best_cell.neuron_idx} (Sparsity: {best_cell.sparsity:.3f})")
            print()
    
    # 稀疏度分布
    sparsity_values = [cell.sparsity for cell in all_cells]
    print(f"SPARSITY DISTRIBUTION:")
    print(f"  High Sparsity (>0.7): {sum(1 for s in sparsity_values if s > 0.7)} cells")
    print(f"  Medium Sparsity (0.4-0.7): {sum(1 for s in sparsity_values if 0.4 <= s <= 0.7)} cells")
    print(f"  Low Sparsity (<0.4): {sum(1 for s in sparsity_values if s < 0.4)} cells")
    print()
    
    print("INTERPRETATION:")
    print("- Sparsity > 0.7: Highly selective distance cells (active only in narrow distance ranges)")
    print("- Sparsity 0.4-0.7: Moderately selective cells (some distance preference)")
    print("- Peak Significance > 2.0: Strong distance tuning")
    print("- Selectivity Index > 0.2: Clear preference for specific distance ranges")
    print("="*60)

# 您可以替换掉 population_coding_viz.py 中的原函数
def plot_population_tiling(distance_cells: Dict[str, List[DistanceCell]],
                          output_dir: str, max_distance: float = 8.0):
    """
    绘制群体编码的瓦片式覆盖 - 采用热力图法
    """
    fig, ax = plt.subplots(figsize=(15, 6))

    # 1. 收集所有细胞并按峰值距离排序
    all_cells = []
    for category in ['close', 'mid', 'far']:
        all_cells.extend(distance_cells[category])
    all_cells.sort(key=lambda x: x.peak_distance)

    if not all_cells:
        print("No distance cells found to plot.")
        return

    print(f"Plotting population tiling with {len(all_cells)} distance cells using heatmap method...")

    # 2. 构建激活矩阵
    # 假设所有cell的distances (bins) 都是一样的
    distance_bins = all_cells[0].distances
    activation_matrix = []
    for cell in all_cells:
        # 归一化并平滑
        raw_curve = cell.tuning_curve
        normalized_curve = raw_curve / np.max(raw_curve) if np.max(raw_curve) > 0 else raw_curve
        smoothed_curve = gaussian_filter1d(normalized_curve, sigma=1.2)
        activation_matrix.append(smoothed_curve)
    
    activation_matrix = np.array(activation_matrix)

    # 3. 使用imshow绘制热力图
    im = ax.imshow(activation_matrix, 
                   aspect='auto', 
                   cmap='magma', # 或者 'viridis', 'jet'
                   origin='lower',
                   extent=[distance_bins[0], distance_bins[-1], 0, len(all_cells)])

    # 4. 美化图表
    ax.set_xlabel('Inter-Agent Distance (m)', fontsize=14, fontweight='bold')
    ax.set_ylabel('Neuron ID', fontsize=14, fontweight='bold')
    ax.set_title('Population Code for Inter-Agent Distance\nTiling of Distance Space by Neural Population',
                 fontsize=16, fontweight='bold', pad=20)
    
    # 添加colorbar
    cbar = fig.colorbar(im, ax=ax, pad=0.02)
    cbar.set_label('Normalized Neural Activation', rotation=270, labelpad=15, fontsize=12)

    # 在Y轴次坐标轴上标注细胞类别
    ax2 = ax.twinx()
    ax2.set_ylim(ax.get_ylim())
    
    # category_positions = []
    # category_labels = []
    # current_pos = 0
    # for category in ['close', 'mid', 'far']:
    #     count = len(distance_cells[category])
    #     if count > 0:
    #         category_positions.append(current_pos + count / 2)
    #         category_labels.append(f'{category.title()}\n(n={count})')
    #     current_pos += count
    #     if category != 'far':
    #          ax.axhline(y=current_pos, color='white', linestyle='--', alpha=0.5)

    # ax2.set_yticks(category_positions)
    # ax2.set_yticklabels(category_labels, fontsize=10, ha='center')
    # ax2.spines['right'].set_visible(True)
    # ax2.spines['right'].set_color('gray')

    plt.tight_layout(rect=[0, 0, 0.95, 1]) # 为colorbar和次坐标轴留出空间

    # 保存图形
    filename = "population_distance_coding_tiling_heatmap.svg"
    save_path = os.path.join(output_dir, filename)

    plt.savefig(save_path, format='svg', bbox_inches='tight', facecolor='white')
    plt.close(fig)

    print(f"Population tiling heatmap saved to: {save_path}")
    return save_path

def create_combined_figure(distance_cells: Dict[str, List[DistanceCell]],
                          output_dir: str, max_distance: float = 8.0,
                          close_threshold: float = 3.0, far_threshold: float = 5.0):
    """创建2x2组合图展示距离编码的涌现 - 稀疏度版本（修复版）"""

    fig = plt.figure(figsize=(16, 12))

    # 选择每个类别的最佳代表（按稀疏度排序）
    representatives = {}
    for category in ['close', 'mid', 'far']:
        if distance_cells[category]:
            # 选择稀疏度最高的神经元作为代表
            best_cell = max(distance_cells[category], key=lambda x: x.sparsity)
            representatives[category] = best_cell

    extent = [-max_distance, max_distance, -max_distance, max_distance]

    # 面板 (i): 近距离细胞
    if 'close' in representatives:
        ax1 = plt.subplot(2, 2, 1)
        cell = representatives['close']
        heatmap = create_2d_activation_heatmap(cell, max_distance)

        im1 = ax1.imshow(heatmap, cmap='jet', origin='lower', extent=extent,
                        aspect='equal', interpolation='bilinear', vmin=0)
        ax1.scatter(0, 0, color='cyan', s=80, marker='*', edgecolors='black', linewidth=1.5)

        # 添加距离圆圈
        for radius, linestyle in [(close_threshold, '--'), (far_threshold, '--'), (max_distance, '-')]:
            circle = plt.Circle((0, 0), radius, fill=False, color='white',
                              linestyle=linestyle, alpha=0.6, linewidth=1)
            ax1.add_patch(circle)
            ax1.text(radius*0.7, radius*0.7, f'{radius}m', color='white',
                    fontsize=8, ha='center', va='center', weight='bold')

        ax1.set_title(f'(i) Close-Distance Cell\nNeuron {cell.neuron_idx}, Peak: {cell.peak_distance:.1f}m',
                     fontweight='bold', fontsize=11)
        ax1.set_xlabel('Relative X (m)', fontweight='bold')
        ax1.set_ylabel('Relative Y (m)', fontweight='bold')
        ax1.set_xlim(-max_distance, max_distance)
        ax1.set_ylim(-max_distance, max_distance)

        # 面板 (ii): 近距离细胞的调谐曲线
        ax2 = plt.subplot(2, 2, 2)
        ax2.plot(cell.distances, cell.tuning_curve, color=NATURE_COLORS['close'],
                linewidth=3, alpha=0.8)
        ax2.fill_between(cell.distances, cell.tuning_curve, alpha=0.3, color=NATURE_COLORS['close'])
        ax2.axvline(cell.peak_distance, color=NATURE_COLORS['close'], linestyle='--', alpha=0.7)
        
        # 添加阈值线
        ax2.axvline(close_threshold, color='red', linestyle=':', alpha=0.7)
        ax2.axvline(far_threshold, color='green', linestyle=':', alpha=0.7)

        ax2.set_title(f'(ii) Close-Distance Tuning\nSparsity = {cell.sparsity:.3f}',
                     fontweight='bold', fontsize=11)
        ax2.set_xlabel('Distance (m)', fontweight='bold')
        ax2.set_ylabel('Activation', fontweight='bold')
        ax2.grid(True, alpha=0.3)
        ax2.set_xlim(0, max_distance)

    # 面板 (iii): 中距离细胞
    if 'mid' in representatives:
        ax3 = plt.subplot(2, 2, 3)
        cell = representatives['mid']
        heatmap = create_2d_activation_heatmap(cell, max_distance)

        im3 = ax3.imshow(heatmap, cmap='jet', origin='lower', extent=extent,
                        aspect='equal', interpolation='bilinear', vmin=0)
        ax3.scatter(0, 0, color='cyan', s=80, marker='*', edgecolors='black', linewidth=1.5)

        for radius, linestyle in [(close_threshold, '--'), (far_threshold, '--'), (max_distance, '-')]:
            circle = plt.Circle((0, 0), radius, fill=False, color='white',
                              linestyle=linestyle, alpha=0.6, linewidth=1)
            ax3.add_patch(circle)
            ax3.text(radius*0.7, radius*0.7, f'{radius}m', color='white',
                    fontsize=8, ha='center', va='center', weight='bold')

        ax3.set_title(f'(iii) Mid-Distance Cell\nNeuron {cell.neuron_idx}, Peak: {cell.peak_distance:.1f}m',
                     fontweight='bold', fontsize=11)
        ax3.set_xlabel('Relative X (m)', fontweight='bold')
        ax3.set_ylabel('Relative Y (m)', fontweight='bold')
        ax3.set_xlim(-max_distance, max_distance)
        ax3.set_ylim(-max_distance, max_distance)

    # 面板 (iv): 群体编码瓦片式覆盖
    ax4 = plt.subplot(2, 2, 4)

    # 收集所有距离细胞
    all_cells = []
    for category in ['close', 'mid', 'far']:
        all_cells.extend(distance_cells[category])
    all_cells.sort(key=lambda x: x.peak_distance)

    # 绘制所有调谐曲线
    for cell in all_cells:
        normalized_curve = cell.tuning_curve / np.max(cell.tuning_curve) if np.max(cell.tuning_curve) > 0 else cell.tuning_curve
        color = NATURE_COLORS[cell.category]
        # 线条透明度与稀疏度成正比，稀疏度越高越明显
        alpha = min(0.9, 0.3 + 0.6 * cell.sparsity)
        ax4.plot(cell.distances, normalized_curve, color=color, alpha=alpha, linewidth=1.5)

    # 添加阈值线
    ax4.axvline(close_threshold, color='red', linestyle=':', alpha=0.7, label=f'Close threshold ({close_threshold}m)')
    ax4.axvline(far_threshold, color='green', linestyle=':', alpha=0.7, label=f'Far threshold ({far_threshold}m)')

    # 添加图例
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color=NATURE_COLORS['close'], lw=2, label=f'Close (n={len(distance_cells["close"])})'),
        Line2D([0], [0], color=NATURE_COLORS['mid'], lw=2, label=f'Mid (n={len(distance_cells["mid"])})'),
        Line2D([0], [0], color=NATURE_COLORS['far'], lw=2, label=f'Far (n={len(distance_cells["far"])})'),
        Line2D([0], [0], color='red', linestyle=':', label=f'Close threshold'),
        Line2D([0], [0], color='green', linestyle=':', label=f'Far threshold')
    ]
    ax4.legend(handles=legend_elements, loc='upper right', fontsize=8)

    ax4.set_title('(iv) Population Code Tiling\nDistance Space Coverage (Line opacity ∝ Sparsity)',
                 fontweight='bold', fontsize=11)
    ax4.set_xlabel('Distance (m)', fontweight='bold')
    ax4.set_ylabel('Normalized Activation', fontweight='bold')
    ax4.grid(True, alpha=0.3)
    ax4.set_xlim(0, max_distance)
    ax4.set_ylim(0, 1.1)

    # 设置整体标题
    fig.suptitle('Emergence of a Population Code for Inter-Agent Distance\n(Based on Sparsity Analysis)',
                fontsize=18, fontweight='bold', y=0.95)

    plt.tight_layout()
    plt.subplots_adjust(top=0.88, hspace=0.3, wspace=0.3)

    # 保存图形
    filename = "population_distance_coding_emergence_sparsity.svg"
    save_path = os.path.join(output_dir, filename)

    plt.savefig(save_path, format='svg', bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close(fig)

    print(f"Combined figure (sparsity-based) saved to: {save_path}")
    return save_path

def main():
    """主函数 - 基于稀疏度的完整版本"""
    parser = argparse.ArgumentParser(description="Population Coding Visualization for Inter-Agent Distance (Sparsity-based)")
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to the trained best_model.pth file.')
    parser.add_argument('--output_dir', type=str, default='population_coding_viz_sparsity',
                       help='Directory to save visualizations.')
    parser.add_argument('--num_reps', type=int, default=400,
                       help='Number of trajectory repetitions for analysis.')
    parser.add_argument('--close_threshold', type=float, default=5.0,
                       help='Threshold for close-distance cells (meters).')
    parser.add_argument('--far_threshold', type=float, default=12.0,
                       help='Threshold for far-distance cells (meters).')
    parser.add_argument('--max_distance', type=float, default=25.0,
                       help='Maximum distance for visualization (meters).')
    parser.add_argument('--min_sparsity', type=float, default=0.3,
                       help='Minimum sparsity threshold for distance cells.')
    parser.add_argument('--min_peak_sig', type=float, default=2.0,
                       help='Minimum peak significance threshold.')
    args = parser.parse_args()

    start_time = time.time()
    os.makedirs(args.output_dir, exist_ok=True)

    # 打印参数信息
    print("="*60)
    print("SPARSITY-BASED DISTANCE CELL ANALYSIS")
    print("="*60)
    print(f"Parameters:")
    print(f"  Close threshold: {args.close_threshold}m")
    print(f"  Far threshold: {args.far_threshold}m")
    print(f"  Max distance: {args.max_distance}m")
    print(f"  Number of repetitions: {args.num_reps}")
    print(f"  Minimum sparsity: {args.min_sparsity}")
    print(f"  Minimum peak significance: {args.min_peak_sig}")
    print()

    # 加载配置和模型
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    print("Loading model...")
    place_cells = PlaceCellEnsemble(
        n_cells=config.PLACE_CELLS_N,
        scale=config.PLACE_CELLS_SCALE,
        pos_min=0, pos_max=config.ENV_SIZE,
        seed=config.SEED
    )
    hd_cells = HeadDirectionCellEnsemble(
        n_cells=config.HD_CELLS_N,
        concentration=config.HD_CELLS_CONCENTRATION,
        seed=config.SEED
    )

    model_config = {
        'HIDDEN_SIZE': config.HIDDEN_SIZE,
        'LATENT_DIM': config.LATENT_DIM,
        'dropout_rate': config.DROPOUT_RATE,
        'ego_token_size': getattr(config, 'ego_token_size', 4)
    }
    model = SocialGridCellNetwork(place_cells, hd_cells, model_config)

    # 加载模型权重
    state_dict = torch.load(args.model_path, map_location=device, weights_only=False)
    if all(key.startswith('module.') for key in state_dict.keys()):
        state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    model.load_state_dict(state_dict)
    model.to(device)
    model.eval()

    print(f"Model loaded. Relational head has {model.relational_head[0].out_features} neurons.")

    # 创建基于稀疏度的分类器
    classifier = DistanceCellClassifier(args.close_threshold, args.far_threshold)
    
    # 临时修改分类器的阈值（如果用户指定了）
    original_classify = classifier.classify_neurons
    def modified_classify_neurons(model, config, device, num_reps, max_distance):
        # 保存原始方法，修改阈值判断逻辑
        distance_cells = {'close': [], 'mid': [], 'far': []}
        
        trajectory_gen = TrajectoryGenerator(config)
        print("Analyzing all neurons for distance tuning using sparsity metrics...")
        
        model.eval()
        with torch.no_grad():
            # ... (前面的数据准备代码相同)
            trajs = trajectory_gen.get_proximity_analysis_trajectories(num_reps=num_reps, max_distance=max_distance)
            
            self_vel_input = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(), 
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)
            
            peer_vel_input = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(), 
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

            outputs = model(self_vel_input, self_init_pos, self_init_hd, peer_vel_input, peer_init_pos, peer_init_hd)
            relational_activations = outputs['relational_activations'].cpu().numpy()
            
            self_pos = trajs['self_pos']
            peer_pos = trajs['peer_pos']
            distances = np.linalg.norm(self_pos - peer_pos, axis=-1).flatten()
            relative_positions = (peer_pos - self_pos).reshape(-1, 2)
            
            for neuron_idx in tqdm(range(relational_activations.shape[-1]), desc="Classifying neurons"):
                activations = relational_activations[:, :, neuron_idx].flatten()
                
                if np.std(activations) < 1e-8:
                    continue
                
                tuning_curve, distance_bins = classifier._compute_tuning_curve(distances, activations)
                
                peak_idx = np.argmax(tuning_curve)
                peak_distance = distance_bins[peak_idx]
                max_activation = tuning_curve[peak_idx]
                
                sparsity = classifier._compute_sparsity(tuning_curve)
                peak_significance = classifier._compute_peak_significance(tuning_curve)
                selectivity_index = classifier._compute_selectivity_index(distances, activations)
                peak_width = classifier._compute_peak_width(tuning_curve, distance_bins)
                
                # 使用用户指定的阈值
                is_distance_cell = (
                    sparsity > args.min_sparsity and
                    peak_significance > args.min_peak_sig and
                    selectivity_index > 0.2 and
                    max_activation > np.mean(activations) + 0.5 * np.std(activations)
                )
                
                if is_distance_cell:
                    if peak_distance <= args.close_threshold:
                        category = 'close'
                    elif peak_distance >= args.far_threshold:
                        category = 'far'
                    else:
                        category = 'mid'
                    
                    distance_cell = DistanceCell(
                        neuron_idx=neuron_idx,
                        category=category,
                        peak_distance=peak_distance,
                        max_activation=max_activation,
                        tuning_curve=tuning_curve,
                        distances=distance_bins,
                        activations=activations,
                        relative_positions=relative_positions,
                        sparsity=sparsity,
                        peak_width=peak_width,
                        peak_significance=peak_significance,
                        selectivity_index=selectivity_index
                    )
                    
                    distance_cells[category].append(distance_cell)
        
        # 按峰值距离排序
        for category in distance_cells:
            distance_cells[category].sort(key=lambda x: x.peak_distance)
        
        return distance_cells
    
    # 分类距离细胞
    distance_cells = modified_classify_neurons(model, config, device, args.num_reps, args.max_distance)

    # 打印详细分析结果
    print_sparsity_analysis_summary(distance_cells)

    # 保存个体距离细胞
    print("\nSaving individual distance cell visualizations...")
    saved_individual = 0
    for category in ['close', 'mid', 'far']:
        # 按稀疏度排序，保存最稀疏的前5个
        sorted_cells = sorted(distance_cells[category], key=lambda x: x.sparsity, reverse=True)
        for cell in sorted_cells[:5]:
            plot_individual_distance_cell(cell, args.output_dir, args.max_distance, 
                                        args.close_threshold, args.far_threshold)
            saved_individual += 1
    
    print(f"Saved {saved_individual} individual distance cell visualizations.")

    # 创建群体编码瓦片图
    print("\nCreating population tiling visualization...")
    plot_population_tiling(distance_cells, args.output_dir, args.max_distance)

    # 创建组合图
    print("\nCreating combined figure...")
    create_combined_figure(distance_cells, args.output_dir, args.max_distance,
                          args.close_threshold, args.far_threshold)

    # 保存分析结果到文件
    results_file = os.path.join(args.output_dir, "sparsity_analysis_results.txt")
    with open(results_file, 'w') as f:
        f.write("SPARSITY-BASED DISTANCE CELL ANALYSIS RESULTS\n")
        f.write("="*50 + "\n\n")
        f.write(f"Analysis Parameters:\n")
        f.write(f"  Model: {args.model_path}\n")
        f.write(f"  Close threshold: {args.close_threshold}m\n")
        f.write(f"  Far threshold: {args.far_threshold}m\n")
        f.write(f"  Min sparsity: {args.min_sparsity}\n")
        f.write(f"  Min peak significance: {args.min_peak_sig}\n")
        f.write(f"  Trajectories: {args.num_reps}\n\n")
        
        all_cells = []
        for category in ['close', 'mid', 'far']:
            all_cells.extend(distance_cells[category])
        
        f.write(f"Summary:\n")
        f.write(f"  Total neurons analyzed: {model.relational_head[0].out_features}\n")
        f.write(f"  Distance cells found: {len(all_cells)}\n")
        f.write(f"  Detection rate: {len(all_cells)/model.relational_head[0].out_features*100:.1f}%\n\n")
        
        for category in ['close', 'mid', 'far']:
            cells = distance_cells[category]
            if cells:
                f.write(f"{category.upper()}-Distance Cells: {len(cells)}\n")
                for cell in cells:
                    f.write(f"  Neuron {cell.neuron_idx}: Peak={cell.peak_distance:.1f}m, "
                           f"Sparsity={cell.sparsity:.3f}, PeakSig={cell.peak_significance:.2f}, "
                           f"Selectivity={cell.selectivity_index:.3f}\n")
                f.write("\n")

    total_time = time.time() - start_time
    print(f"\nAll visualizations and analysis saved in '{args.output_dir}'")
    print(f"Results summary saved to: {results_file}")
    print(f"Total execution time: {total_time:.2f} seconds")
    
    # 最终统计
    total_neurons = model.relational_head[0].out_features
    total_distance_cells = sum(len(distance_cells[cat]) for cat in distance_cells)
    detection_rate = total_distance_cells / total_neurons * 100
    
    print(f"\nFINAL STATISTICS:")
    print(f"  Total neurons: {total_neurons}")
    print(f"  Distance cells detected: {total_distance_cells}")
    print(f"  Detection rate: {detection_rate:.1f}%")
    
    if detection_rate < 30:
        print(f"\nNOTE: Low detection rate. Consider lowering thresholds:")
        print(f"  --min_sparsity (current: {args.min_sparsity})")
        print(f"  --min_peak_sig (current: {args.min_peak_sig})")

if __name__ == '__main__':
    main()