<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="830.816413pt" height="352.267438pt" viewBox="0 0 830.816413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:42.668583</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M -0 352.267438 
L 830.816413 352.267438 
L 830.816413 0 
L -0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p3fd2611123)">
    <image xlink:href="data:image/png;base64,
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" id="image1ac069ae3f" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p3fd2611123)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p3fd2611123)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p3fd2611123)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m66941779e4" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m66941779e4" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m66941779e4" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m66941779e4" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m66941779e4" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m66941779e4" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m66941779e4" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m66941779e4" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m66941779e4" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m66941779e4" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m1f63a33ede" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m1f63a33ede" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m1f63a33ede" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m1f63a33ede" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m1f63a33ede" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m1f63a33ede" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m1f63a33ede" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m1f63a33ede" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m1f63a33ede" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p3fd2611123)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m1f63a33ede" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Far-Distance Cell (Neuron 5) -->
    <g transform="translate(81.107844 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-46" d="M 588 4666 
L 3834 4666 
L 3834 3756 
L 1791 3756 
L 1791 2888 
L 3713 2888 
L 3713 1978 
L 1791 1978 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-46"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="62.435547"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="129.916016"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="179.232422"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="220.736328"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="303.744141"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="338.021484"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="397.542969"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="445.345703"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="512.826172"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="584.017578"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="643.294922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="711.117188"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="745.931641"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="819.320312"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="887.142578"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="921.419922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="955.697266"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="990.511719"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1036.214844"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1119.90625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1187.728516"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1258.919922"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1308.236328"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1376.9375"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1448.128906"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1482.943359"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1552.523438"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m3298d53dc7" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p3fd2611123)">
     <use xlink:href="#m3298d53dc7" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m3298d53dc7" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 451.595015 314.62525 
L 820.753288 314.62525 
L 820.753288 44.84925 
L 451.595015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="mb75f1e93d1" d="M 456.205265 -37.642188 
L 456.205265 -37.642188 
L 465.425764 -37.642188 
L 474.646263 -37.642188 
L 483.866762 -37.642188 
L 493.087262 -37.642188 
L 502.307761 -37.642188 
L 511.52826 -37.642188 
L 520.748759 -37.642188 
L 529.969258 -37.642188 
L 539.189757 -37.642188 
L 548.410257 -37.642188 
L 557.630756 -37.64617 
L 566.851255 -37.642188 
L 576.071754 -37.680016 
L 585.292253 -37.684281 
L 594.512752 -37.962641 
L 603.733252 -41.104039 
L 612.953751 -46.875204 
L 622.17425 -60.865529 
L 631.394749 -83.048784 
L 640.615248 -138.937581 
L 649.835747 -119.973291 
L 659.056246 -160.372071 
L 668.276746 -153.168241 
L 677.497245 -166.100463 
L 686.717744 -174.728152 
L 695.938243 -224.50331 
L 705.158742 -294.571711 
L 714.379241 -256.332637 
L 723.599741 -113.464617 
L 723.599741 -37.642188 
L 723.599741 -37.642188 
L 714.379241 -37.642188 
L 705.158742 -37.642188 
L 695.938243 -37.642188 
L 686.717744 -37.642188 
L 677.497245 -37.642188 
L 668.276746 -37.642188 
L 659.056246 -37.642188 
L 649.835747 -37.642188 
L 640.615248 -37.642188 
L 631.394749 -37.642188 
L 622.17425 -37.642188 
L 612.953751 -37.642188 
L 603.733252 -37.642188 
L 594.512752 -37.642188 
L 585.292253 -37.642188 
L 576.071754 -37.642188 
L 566.851255 -37.642188 
L 557.630756 -37.642188 
L 548.410257 -37.642188 
L 539.189757 -37.642188 
L 529.969258 -37.642188 
L 520.748759 -37.642188 
L 511.52826 -37.642188 
L 502.307761 -37.642188 
L 493.087262 -37.642188 
L 483.866762 -37.642188 
L 474.646263 -37.642188 
L 465.425764 -37.642188 
L 456.205265 -37.642188 
z
" style="stroke: #33a02c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#p77b57f06c3)">
     <use xlink:href="#mb75f1e93d1" x="0" y="352.267438" style="fill: #33a02c; fill-opacity: 0.3; stroke: #33a02c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 451.595015 314.62525 
L 451.595015 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m66941779e4" x="451.595015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(448.73189 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 497.739799 314.62525 
L 497.739799 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m66941779e4" x="497.739799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(494.876674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 543.884584 314.62525 
L 543.884584 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m66941779e4" x="543.884584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(541.021459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 590.029368 314.62525 
L 590.029368 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m66941779e4" x="590.029368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(587.166243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 636.174152 314.62525 
L 636.174152 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m66941779e4" x="636.174152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(633.311027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 682.318936 314.62525 
L 682.318936 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m66941779e4" x="682.318936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(679.455811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 728.46372 314.62525 
L 728.46372 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m66941779e4" x="728.46372" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(725.600595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 774.608504 314.62525 
L 774.608504 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m66941779e4" x="774.608504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(771.745379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 820.753288 314.62525 
L 820.753288 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m66941779e4" x="820.753288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(817.890163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(558.831261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 451.595015 314.62525 
L 820.753288 314.62525 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m1f63a33ede" x="451.595015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.00 -->
      <g transform="translate(424.555953 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 451.595015 275.234326 
L 820.753288 275.234326 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m1f63a33ede" x="451.595015" y="275.234326" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.02 -->
      <g transform="translate(424.555953 278.653623) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-32" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 451.595015 235.843402 
L 820.753288 235.843402 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m1f63a33ede" x="451.595015" y="235.843402" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.04 -->
      <g transform="translate(424.555953 239.262699) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-34" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 451.595015 196.452478 
L 820.753288 196.452478 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m1f63a33ede" x="451.595015" y="196.452478" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.06 -->
      <g transform="translate(424.555953 199.871775) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-36" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 451.595015 157.061554 
L 820.753288 157.061554 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m1f63a33ede" x="451.595015" y="157.061554" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.08 -->
      <g transform="translate(424.555953 160.480851) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-38" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 451.595015 117.67063 
L 820.753288 117.67063 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m1f63a33ede" x="451.595015" y="117.67063" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.10 -->
      <g transform="translate(424.555953 121.089926) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 451.595015 78.279705 
L 820.753288 78.279705 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m1f63a33ede" x="451.595015" y="78.279705" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 0.12 -->
      <g transform="translate(424.555953 81.699002) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-32" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_43">
     <!-- Neural Activation -->
     <g transform="translate(418.268297 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_69">
    <path d="M 456.205265 314.62525 
L 465.425764 314.62525 
L 474.646263 314.62525 
L 483.866762 314.62525 
L 493.087262 314.62525 
L 502.307761 314.62525 
L 511.52826 314.62525 
L 520.748759 314.62525 
L 529.969258 314.62525 
L 539.189757 314.62525 
L 548.410257 314.62525 
L 557.630756 314.621268 
L 566.851255 314.62525 
L 576.071754 314.587421 
L 585.292253 314.583157 
L 594.512752 314.304796 
L 603.733252 311.163399 
L 612.953751 305.392233 
L 622.17425 291.401909 
L 631.394749 269.218653 
L 640.615248 213.329856 
L 649.835747 232.294147 
L 659.056246 191.895366 
L 668.276746 199.099196 
L 677.497245 186.166974 
L 686.717744 177.539285 
L 695.938243 127.764127 
L 705.158742 57.695726 
L 714.379241 95.9348 
L 723.599741 238.802821 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_70">
    <path d="M 705.158742 314.62525 
L 705.158742 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #33a02c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_71">
    <path d="M 590.029368 314.62525 
L 590.029368 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_72">
    <path d="M 682.318936 314.62525 
L 682.318936 44.84925 
" clip-path="url(#p77b57f06c3)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 451.595015 314.62525 
L 451.595015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 451.595015 314.62525 
L 820.753288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_44">
    <g id="patch_15">
     <path d="M 707.854124 114.0718 
L 802.295374 114.0718 
Q 805.495374 114.0718 805.495374 110.8718 
L 805.495374 58.33805 
Q 805.495374 55.13805 802.295374 55.13805 
L 707.854124 55.13805 
Q 704.654124 55.13805 704.654124 58.33805 
L 704.654124 110.8718 
Q 704.654124 114.0718 707.854124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 5.5m -->
    <g transform="translate(720.029124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-35" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-35" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.130 -->
    <g transform="translate(715.021624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-31" x="900.056641"/>
     <use xlink:href="#DejaVuSans-33" x="963.679688"/>
     <use xlink:href="#DejaVuSans-30" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.705 -->
    <g transform="translate(742.125374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-37" x="561.255859"/>
     <use xlink:href="#DejaVuSans-30" x="624.878906"/>
     <use xlink:href="#DejaVuSans-35" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.49 -->
    <g transform="translate(709.307874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-34" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-39" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.879 -->
    <g transform="translate(707.854124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-38" x="989.607422"/>
     <use xlink:href="#DejaVuSans-37" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-39" x="1116.853516"/>
    </g>
    <!-- Peak Width: 0.6m -->
    <g transform="translate(731.771624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-30" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-36" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_45">
    <!-- Distance Tuning Curve -->
    <g transform="translate(560.360402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 5.5m, Sparsity: 0.705 -->
    <g transform="translate(542.435402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-37" d="M 428 4666 
L 3944 4666 
L 3944 3988 
L 2125 0 
L 953 0 
L 2675 3781 
L 428 3781 
L 428 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-37" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m6208353001" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p77b57f06c3)">
     <use xlink:href="#m6208353001" x="705.158742" y="57.695726" style="fill: #33a02c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 457.195015 86.47675 
L 574.102515 86.47675 
Q 575.702515 86.47675 575.702515 84.87675 
L 575.702515 50.44925 
Q 575.702515 48.84925 574.102515 48.84925 
L 457.195015 48.84925 
Q 455.595015 48.84925 455.595015 50.44925 
L 455.595015 84.87675 
Q 455.595015 86.47675 457.195015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_73">
     <path d="M 458.795015 55.328 
L 466.795015 55.328 
L 474.795015 55.328 
" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_46">
     <!-- Tuning Curve -->
     <g transform="translate(481.195015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_74">
     <path d="M 458.795015 67.0705 
L 466.795015 67.0705 
L 474.795015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(481.195015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_75">
     <path d="M 458.795015 78.813 
L 466.795015 78.813 
L 474.795015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_48">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(481.195015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.34782 287.64765 
L 341.13886 287.64765 
L 341.13886 71.82685 
L 330.34782 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image0b6e548121" transform="scale(1 -1) translate(0 -215.52)" x="330.24" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_17">
     <g id="line2d_76">
      <defs>
       <path id="mbbea456d2b" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mbbea456d2b" x="341.13886" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.000 -->
      <g transform="translate(348.13886 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_77">
      <g>
       <use xlink:href="#mbbea456d2b" x="341.13886" y="261.208162" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.025 -->
      <g transform="translate(348.13886 264.627459) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-32" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_78">
      <g>
       <use xlink:href="#mbbea456d2b" x="341.13886" y="234.768674" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.050 -->
      <g transform="translate(348.13886 238.187971) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_79">
      <g>
       <use xlink:href="#mbbea456d2b" x="341.13886" y="208.329187" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.075 -->
      <g transform="translate(348.13886 211.748484) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-37" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_80">
      <g>
       <use xlink:href="#mbbea456d2b" x="341.13886" y="181.889699" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.100 -->
      <g transform="translate(348.13886 185.308996) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_81">
      <g>
       <use xlink:href="#mbbea456d2b" x="341.13886" y="155.450211" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.125 -->
      <g transform="translate(348.13886 158.869508) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-32" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_82">
      <g>
       <use xlink:href="#mbbea456d2b" x="341.13886" y="129.010723" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 0.150 -->
      <g transform="translate(348.13886 132.43002) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_83">
      <g>
       <use xlink:href="#mbbea456d2b" x="341.13886" y="102.571236" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 0.175 -->
      <g transform="translate(348.13886 105.990533) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-37" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_84">
      <g>
       <use xlink:href="#mbbea456d2b" x="341.13886" y="76.131748" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_57">
      <!-- 0.200 -->
      <g transform="translate(348.13886 79.551045) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="text_58">
     <!-- Neural Activation -->
     <g transform="translate(380.545891 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.34782 287.64765 
L 335.74334 287.64765 
L 341.13886 287.64765 
L 341.13886 71.82685 
L 335.74334 71.82685 
L 330.34782 71.82685 
L 330.34782 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p3fd2611123">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="p77b57f06c3">
   <rect x="451.595015" y="44.84925" width="369.158273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
