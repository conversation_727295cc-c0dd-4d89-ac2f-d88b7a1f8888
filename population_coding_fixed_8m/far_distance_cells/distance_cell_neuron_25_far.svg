<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="830.792413pt" height="352.267438pt" viewBox="0 0 830.792413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:43.801422</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 830.792413 352.267438 
L 830.792413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p0beed6ba1f)">
    <image xlink:href="data:image/png;base64,
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" id="image92ef2817bc" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p0beed6ba1f)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p0beed6ba1f)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p0beed6ba1f)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m478d0e617e" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m478d0e617e" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m478d0e617e" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m478d0e617e" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m478d0e617e" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m478d0e617e" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m478d0e617e" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m478d0e617e" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m478d0e617e" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m478d0e617e" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="md184345e19" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#md184345e19" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845938 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#md184345e19" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845938 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#md184345e19" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845938 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#md184345e19" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845938 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#md184345e19" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#md184345e19" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#md184345e19" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#md184345e19" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p0beed6ba1f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#md184345e19" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Far-Distance Cell (Neuron 25) -->
    <g transform="translate(76.933156 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-46" d="M 588 4666 
L 3834 4666 
L 3834 3756 
L 1791 3756 
L 1791 2888 
L 3713 2888 
L 3713 1978 
L 1791 1978 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-46"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="62.435547"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="129.916016"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="179.232422"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="220.736328"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="303.744141"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="338.021484"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="397.542969"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="445.345703"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="512.826172"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="584.017578"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="643.294922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="711.117188"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="745.931641"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="819.320312"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="887.142578"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="921.419922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="955.697266"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="990.511719"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1036.214844"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1119.90625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1187.728516"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1258.919922"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1308.236328"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1376.9375"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1448.128906"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1482.943359"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1552.523438"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1622.103516"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m230c12e501" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p0beed6ba1f)">
     <use xlink:href="#m230c12e501" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m230c12e501" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 451.541015 314.62525 
L 820.729288 314.62525 
L 820.729288 44.84925 
L 451.541015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m1027fe2a9b" d="M 456.15164 -37.642188 
L 456.15164 -37.642188 
L 465.372888 -37.642188 
L 474.594137 -37.642188 
L 483.815385 -37.642188 
L 493.036633 -37.642188 
L 502.257882 -37.642188 
L 511.47913 -37.642188 
L 520.700379 -37.642188 
L 529.921627 -37.642188 
L 539.142876 -37.642188 
L 548.364124 -37.903932 
L 557.585373 -38.203764 
L 566.806621 -38.436742 
L 576.02787 -38.97114 
L 585.249118 -38.924741 
L 594.470367 -39.996768 
L 603.691615 -43.162139 
L 612.912864 -60.118602 
L 622.134112 -54.911812 
L 631.355361 -81.749838 
L 640.576609 -142.729201 
L 649.797858 -140.209502 
L 659.019106 -150.615147 
L 668.240355 -170.435773 
L 677.461603 -178.768166 
L 686.682851 -156.155816 
L 695.9041 -224.099056 
L 705.125348 -294.571711 
L 714.346597 -242.544017 
L 723.567845 -126.991154 
L 723.567845 -37.642188 
L 723.567845 -37.642188 
L 714.346597 -37.642188 
L 705.125348 -37.642188 
L 695.9041 -37.642188 
L 686.682851 -37.642188 
L 677.461603 -37.642188 
L 668.240355 -37.642188 
L 659.019106 -37.642188 
L 649.797858 -37.642188 
L 640.576609 -37.642188 
L 631.355361 -37.642188 
L 622.134112 -37.642188 
L 612.912864 -37.642188 
L 603.691615 -37.642188 
L 594.470367 -37.642188 
L 585.249118 -37.642188 
L 576.02787 -37.642188 
L 566.806621 -37.642188 
L 557.585373 -37.642188 
L 548.364124 -37.642188 
L 539.142876 -37.642188 
L 529.921627 -37.642188 
L 520.700379 -37.642188 
L 511.47913 -37.642188 
L 502.257882 -37.642188 
L 493.036633 -37.642188 
L 483.815385 -37.642188 
L 474.594137 -37.642188 
L 465.372888 -37.642188 
L 456.15164 -37.642188 
z
" style="stroke: #33a02c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pb98cb9dc8d)">
     <use xlink:href="#m1027fe2a9b" x="0" y="352.267438" style="fill: #33a02c; fill-opacity: 0.3; stroke: #33a02c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 451.541015 314.62525 
L 451.541015 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m478d0e617e" x="451.541015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(448.67789 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 497.689549 314.62525 
L 497.689549 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m478d0e617e" x="497.689549" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(494.826424 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 543.838084 314.62525 
L 543.838084 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m478d0e617e" x="543.838084" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(540.974959 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 589.986618 314.62525 
L 589.986618 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m478d0e617e" x="589.986618" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(587.123493 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 636.135152 314.62525 
L 636.135152 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m478d0e617e" x="636.135152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(633.272027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 682.283686 314.62525 
L 682.283686 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m478d0e617e" x="682.283686" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(679.420561 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 728.43222 314.62525 
L 728.43222 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m478d0e617e" x="728.43222" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(725.569095 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 774.580754 314.62525 
L 774.580754 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m478d0e617e" x="774.580754" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(771.717629 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 820.729288 314.62525 
L 820.729288 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m478d0e617e" x="820.729288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(817.866163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(558.792261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 451.541015 314.62525 
L 820.729288 314.62525 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#md184345e19" x="451.541015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.000 -->
      <g transform="translate(418.775703 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 451.541015 283.339446 
L 820.729288 283.339446 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#md184345e19" x="451.541015" y="283.339446" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.025 -->
      <g transform="translate(418.775703 286.758743) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-32" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 451.541015 252.053642 
L 820.729288 252.053642 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#md184345e19" x="451.541015" y="252.053642" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.050 -->
      <g transform="translate(418.775703 255.472939) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 451.541015 220.767839 
L 820.729288 220.767839 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#md184345e19" x="451.541015" y="220.767839" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.075 -->
      <g transform="translate(418.775703 224.187135) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-37" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 451.541015 189.482035 
L 820.729288 189.482035 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#md184345e19" x="451.541015" y="189.482035" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.100 -->
      <g transform="translate(418.775703 192.901332) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 451.541015 158.196231 
L 820.729288 158.196231 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#md184345e19" x="451.541015" y="158.196231" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.125 -->
      <g transform="translate(418.775703 161.615528) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-32" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 451.541015 126.910427 
L 820.729288 126.910427 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#md184345e19" x="451.541015" y="126.910427" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 0.150 -->
      <g transform="translate(418.775703 130.329724) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 451.541015 95.624623 
L 820.729288 95.624623 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#md184345e19" x="451.541015" y="95.624623" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 0.175 -->
      <g transform="translate(418.775703 99.04392) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-37" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_71">
      <path d="M 451.541015 64.338819 
L 820.729288 64.338819 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_72">
      <g>
       <use xlink:href="#md184345e19" x="451.541015" y="64.338819" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_44">
      <!-- 0.200 -->
      <g transform="translate(418.775703 67.758116) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="text_45">
     <!-- Neural Activation -->
     <g transform="translate(412.488047 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_73">
    <path d="M 456.15164 314.62525 
L 465.372888 314.62525 
L 474.594137 314.62525 
L 483.815385 314.62525 
L 493.036633 314.62525 
L 502.257882 314.62525 
L 511.47913 314.62525 
L 520.700379 314.62525 
L 529.921627 314.62525 
L 539.142876 314.62525 
L 548.364124 314.363505 
L 557.585373 314.063673 
L 566.806621 313.830695 
L 576.02787 313.296298 
L 585.249118 313.342697 
L 594.470367 312.270669 
L 603.691615 309.105298 
L 612.912864 292.148835 
L 622.134112 297.355625 
L 631.355361 270.517599 
L 640.576609 209.538237 
L 649.797858 212.057936 
L 659.019106 201.652291 
L 668.240355 181.831664 
L 677.461603 173.499271 
L 686.682851 196.111621 
L 695.9041 128.168381 
L 705.125348 57.695726 
L 714.346597 109.723421 
L 723.567845 225.276284 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_74">
    <path d="M 705.125348 314.62525 
L 705.125348 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #33a02c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_75">
    <path d="M 589.986618 314.62525 
L 589.986618 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_76">
    <path d="M 682.283686 314.62525 
L 682.283686 44.84925 
" clip-path="url(#pb98cb9dc8d)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 451.541015 314.62525 
L 451.541015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 451.541015 314.62525 
L 820.729288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_46">
    <g id="patch_15">
     <path d="M 707.828624 114.0718 
L 802.269874 114.0718 
Q 805.469874 114.0718 805.469874 110.8718 
L 805.469874 58.33805 
Q 805.469874 55.13805 802.269874 55.13805 
L 707.828624 55.13805 
Q 704.628624 55.13805 704.628624 58.33805 
L 704.628624 110.8718 
Q 704.628624 114.0718 707.828624 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 5.5m -->
    <g transform="translate(720.003624 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-35" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-35" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.205 -->
    <g transform="translate(714.996124 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-32" x="900.056641"/>
     <use xlink:href="#DejaVuSans-30" x="963.679688"/>
     <use xlink:href="#DejaVuSans-35" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.690 -->
    <g transform="translate(742.099874 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-36" x="561.255859"/>
     <use xlink:href="#DejaVuSans-39" x="624.878906"/>
     <use xlink:href="#DejaVuSans-30" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.52 -->
    <g transform="translate(709.282374 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-35" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-32" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.845 -->
    <g transform="translate(707.828624 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-38" x="989.607422"/>
     <use xlink:href="#DejaVuSans-34" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-35" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.0m -->
    <g transform="translate(731.746124 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-30" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_47">
    <!-- Distance Tuning Curve -->
    <g transform="translate(560.321402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 5.5m, Sparsity: 0.690 -->
    <g transform="translate(542.396402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-36" d="M 2316 2303 
Q 2000 2303 1842 2098 
Q 1684 1894 1684 1484 
Q 1684 1075 1842 870 
Q 2000 666 2316 666 
Q 2634 666 2792 870 
Q 2950 1075 2950 1484 
Q 2950 1894 2792 2098 
Q 2634 2303 2316 2303 
z
M 3803 4544 
L 3803 3681 
Q 3506 3822 3243 3889 
Q 2981 3956 2731 3956 
Q 2194 3956 1894 3657 
Q 1594 3359 1544 2772 
Q 1750 2925 1990 3001 
Q 2231 3078 2516 3078 
Q 3231 3078 3670 2659 
Q 4109 2241 4109 1563 
Q 4109 813 3618 361 
Q 3128 -91 2303 -91 
Q 1394 -91 895 523 
Q 397 1138 397 2266 
Q 397 3422 980 4083 
Q 1563 4744 2578 4744 
Q 2900 4744 3203 4694 
Q 3506 4644 3803 4544 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-36" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m5b1100bd3e" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pb98cb9dc8d)">
     <use xlink:href="#m5b1100bd3e" x="705.125348" y="57.695726" style="fill: #33a02c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 457.141015 86.47675 
L 574.048515 86.47675 
Q 575.648515 86.47675 575.648515 84.87675 
L 575.648515 50.44925 
Q 575.648515 48.84925 574.048515 48.84925 
L 457.141015 48.84925 
Q 455.541015 48.84925 455.541015 50.44925 
L 455.541015 84.87675 
Q 455.541015 86.47675 457.141015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_77">
     <path d="M 458.741015 55.328 
L 466.741015 55.328 
L 474.741015 55.328 
" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_48">
     <!-- Tuning Curve -->
     <g transform="translate(481.141015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_78">
     <path d="M 458.741015 67.0705 
L 466.741015 67.0705 
L 474.741015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_49">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(481.141015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_79">
     <path d="M 458.741015 78.813 
L 466.741015 78.813 
L 474.741015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_50">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(481.141015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.34932 287.64765 
L 341.14036 287.64765 
L 341.14036 71.82685 
L 330.34932 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image19a168cd88" transform="scale(1 -1) translate(0 -215.52)" x="330.24" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_19">
     <g id="line2d_80">
      <defs>
       <path id="md5b653d86b" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#md5b653d86b" x="341.14036" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.00 -->
      <g transform="translate(348.14036 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_81">
      <g>
       <use xlink:href="#md5b653d86b" x="341.14036" y="252.518717" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.05 -->
      <g transform="translate(348.14036 255.938014) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_82">
      <g>
       <use xlink:href="#md5b653d86b" x="341.14036" y="217.389785" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.10 -->
      <g transform="translate(348.14036 220.809082) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_83">
      <g>
       <use xlink:href="#md5b653d86b" x="341.14036" y="182.260852" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.15 -->
      <g transform="translate(348.14036 185.680149) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_84">
      <g>
       <use xlink:href="#md5b653d86b" x="341.14036" y="147.13192" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 0.20 -->
      <g transform="translate(348.14036 150.551217) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_85">
      <g>
       <use xlink:href="#md5b653d86b" x="341.14036" y="112.002987" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 0.25 -->
      <g transform="translate(348.14036 115.422284) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_86">
      <g>
       <use xlink:href="#md5b653d86b" x="341.14036" y="76.874055" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_57">
      <!-- 0.30 -->
      <g transform="translate(348.14036 80.293352) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_58">
     <!-- Neural Activation -->
     <g transform="translate(374.821141 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.34932 287.64765 
L 335.74484 287.64765 
L 341.14036 287.64765 
L 341.14036 71.82685 
L 335.74484 71.82685 
L 330.34932 71.82685 
L 330.34932 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p0beed6ba1f">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pb98cb9dc8d">
   <rect x="451.541015" y="44.84925" width="369.188273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
