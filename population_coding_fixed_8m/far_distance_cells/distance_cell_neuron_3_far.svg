<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:45.861402</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p8001772bc9)">
    <image xlink:href="data:image/png;base64,
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" id="image0b7ebc475a" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p8001772bc9)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p8001772bc9)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p8001772bc9)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="ma1505fd57d" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#ma1505fd57d" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#ma1505fd57d" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#ma1505fd57d" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#ma1505fd57d" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#ma1505fd57d" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#ma1505fd57d" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#ma1505fd57d" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#ma1505fd57d" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#ma1505fd57d" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="mc672ab8218" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mc672ab8218" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#mc672ab8218" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#mc672ab8218" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#mc672ab8218" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#mc672ab8218" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#mc672ab8218" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#mc672ab8218" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#mc672ab8218" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p8001772bc9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#mc672ab8218" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Far-Distance Cell (Neuron 3) -->
    <g transform="translate(81.107844 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-46" d="M 588 4666 
L 3834 4666 
L 3834 3756 
L 1791 3756 
L 1791 2888 
L 3713 2888 
L 3713 1978 
L 1791 1978 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-46"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="62.435547"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="129.916016"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="179.232422"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="220.736328"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="303.744141"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="338.021484"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="397.542969"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="445.345703"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="512.826172"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="584.017578"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="643.294922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="711.117188"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="745.931641"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="819.320312"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="887.142578"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="921.419922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="955.697266"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="990.511719"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1036.214844"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1119.90625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1187.728516"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1258.919922"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1308.236328"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1376.9375"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1448.128906"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1482.943359"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1552.523438"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="mdd37a3a694" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p8001772bc9)">
     <use xlink:href="#mdd37a3a694" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#mdd37a3a694" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m7bdf526651" d="M 440.975667 -37.642188 
L 440.975667 -37.642188 
L 450.408971 -40.005112 
L 459.842275 -39.891099 
L 469.275579 -39.99456 
L 478.708883 -40.830757 
L 488.142187 -40.590549 
L 497.575491 -39.674074 
L 507.008795 -40.311581 
L 516.442099 -41.367924 
L 525.875403 -39.382937 
L 535.308707 -42.071494 
L 544.742011 -46.087623 
L 554.175315 -51.778993 
L 563.608619 -49.978288 
L 573.041923 -58.572699 
L 582.475227 -53.636525 
L 591.908531 -78.100901 
L 601.341835 -83.924241 
L 610.775139 -100.874083 
L 620.208443 -152.279437 
L 629.641747 -169.212428 
L 639.075051 -150.01227 
L 648.508355 -202.161547 
L 657.941658 -199.827464 
L 667.374962 -211.400361 
L 676.808266 -253.837086 
L 686.24157 -294.571711 
L 695.674874 -273.787311 
L 705.108178 -244.119859 
L 714.541482 -200.894644 
L 714.541482 -37.642188 
L 714.541482 -37.642188 
L 705.108178 -37.642188 
L 695.674874 -37.642188 
L 686.24157 -37.642188 
L 676.808266 -37.642188 
L 667.374962 -37.642188 
L 657.941658 -37.642188 
L 648.508355 -37.642188 
L 639.075051 -37.642188 
L 629.641747 -37.642188 
L 620.208443 -37.642188 
L 610.775139 -37.642188 
L 601.341835 -37.642188 
L 591.908531 -37.642188 
L 582.475227 -37.642188 
L 573.041923 -37.642188 
L 563.608619 -37.642188 
L 554.175315 -37.642188 
L 544.742011 -37.642188 
L 535.308707 -37.642188 
L 525.875403 -37.642188 
L 516.442099 -37.642188 
L 507.008795 -37.642188 
L 497.575491 -37.642188 
L 488.142187 -37.642188 
L 478.708883 -37.642188 
L 469.275579 -37.642188 
L 459.842275 -37.642188 
L 450.408971 -37.642188 
L 440.975667 -37.642188 
z
" style="stroke: #33a02c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#p280ec922ed)">
     <use xlink:href="#m7bdf526651" x="0" y="352.267438" style="fill: #33a02c; fill-opacity: 0.3; stroke: #33a02c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#ma1505fd57d" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#ma1505fd57d" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#ma1505fd57d" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#ma1505fd57d" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#ma1505fd57d" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#ma1505fd57d" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#ma1505fd57d" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#ma1505fd57d" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#ma1505fd57d" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#mc672ab8218" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 274.623189 
L 813.937288 274.623189 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#mc672ab8218" x="436.259015" y="274.623189" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.2 -->
      <g transform="translate(414.946203 278.042486) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 234.621127 
L 813.937288 234.621127 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#mc672ab8218" x="436.259015" y="234.621127" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.4 -->
      <g transform="translate(414.946203 238.040424) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 194.619066 
L 813.937288 194.619066 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#mc672ab8218" x="436.259015" y="194.619066" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.6 -->
      <g transform="translate(414.946203 198.038363) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 154.617005 
L 813.937288 154.617005 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#mc672ab8218" x="436.259015" y="154.617005" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.8 -->
      <g transform="translate(414.946203 158.036302) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 114.614944 
L 813.937288 114.614944 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#mc672ab8218" x="436.259015" y="114.614944" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 1.0 -->
      <g transform="translate(414.946203 118.034241) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 436.259015 74.612882 
L 813.937288 74.612882 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#mc672ab8218" x="436.259015" y="74.612882" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 1.2 -->
      <g transform="translate(414.946203 78.032179) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_43">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_69">
    <path d="M 440.975667 314.62525 
L 450.408971 312.262326 
L 459.842275 312.376339 
L 469.275579 312.272877 
L 478.708883 311.436681 
L 488.142187 311.676888 
L 497.575491 312.593363 
L 507.008795 311.955856 
L 516.442099 310.899514 
L 525.875403 312.8845 
L 535.308707 310.195944 
L 544.742011 306.179814 
L 554.175315 300.488444 
L 563.608619 302.289149 
L 573.041923 293.694738 
L 582.475227 298.630913 
L 591.908531 274.166536 
L 601.341835 268.343196 
L 610.775139 251.393355 
L 620.208443 199.988 
L 629.641747 183.05501 
L 639.075051 202.255168 
L 648.508355 150.105891 
L 657.941658 152.439974 
L 667.374962 140.867076 
L 676.808266 98.430352 
L 686.24157 57.695726 
L 695.674874 78.480126 
L 705.108178 108.147578 
L 714.541482 151.372793 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_70">
    <path d="M 686.24157 314.62525 
L 686.24157 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #33a02c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_71">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_72">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#p280ec922ed)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_44">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 5.3m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-35" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-33" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 1.285 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-31" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-32" x="900.056641"/>
     <use xlink:href="#DejaVuSans-38" x="963.679688"/>
     <use xlink:href="#DejaVuSans-35" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.593 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-35" x="561.255859"/>
     <use xlink:href="#DejaVuSans-39" x="624.878906"/>
     <use xlink:href="#DejaVuSans-33" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.01 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-30" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-31" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.758 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-37" x="989.607422"/>
     <use xlink:href="#DejaVuSans-35" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-38" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.8m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-38" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_45">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 5.3m, Sparsity: 0.593 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m366b4a8243" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p280ec922ed)">
     <use xlink:href="#m366b4a8243" x="686.24157" y="57.695726" style="fill: #33a02c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 441.859015 86.47675 
L 558.766515 86.47675 
Q 560.366515 86.47675 560.366515 84.87675 
L 560.366515 50.44925 
Q 560.366515 48.84925 558.766515 48.84925 
L 441.859015 48.84925 
Q 440.259015 48.84925 440.259015 50.44925 
L 440.259015 84.87675 
Q 440.259015 86.47675 441.859015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_73">
     <path d="M 443.459015 55.328 
L 451.459015 55.328 
L 459.459015 55.328 
" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_46">
     <!-- Tuning Curve -->
     <g transform="translate(465.859015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_74">
     <path d="M 443.459015 67.0705 
L 451.459015 67.0705 
L 459.459015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(465.859015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_75">
     <path d="M 443.459015 78.813 
L 451.459015 78.813 
L 459.459015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_48">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(465.859015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="imagee82e120c3f" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_17">
     <g id="line2d_76">
      <defs>
       <path id="m44a6037598" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m44a6037598" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_77">
      <g>
       <use xlink:href="#m44a6037598" x="341.56486" y="247.264525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.2 -->
      <g transform="translate(348.56486 250.683822) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_78">
      <g>
       <use xlink:href="#m44a6037598" x="341.56486" y="206.8814" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.4 -->
      <g transform="translate(348.56486 210.300697) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_79">
      <g>
       <use xlink:href="#m44a6037598" x="341.56486" y="166.498275" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.6 -->
      <g transform="translate(348.56486 169.917572) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_80">
      <g>
       <use xlink:href="#m44a6037598" x="341.56486" y="126.115151" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.8 -->
      <g transform="translate(348.56486 129.534447) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_81">
      <g>
       <use xlink:href="#m44a6037598" x="341.56486" y="85.732026" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 1.0 -->
      <g transform="translate(348.56486 89.151323) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_55">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p8001772bc9">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="p280ec922ed">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
