<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:44.839442</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p6f2237a0c0)">
    <image xlink:href="data:image/png;base64,
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" id="imagea54f9706e0" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p6f2237a0c0)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p6f2237a0c0)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p6f2237a0c0)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m36000909b5" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m36000909b5" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m36000909b5" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m36000909b5" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m36000909b5" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m36000909b5" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m36000909b5" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m36000909b5" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m36000909b5" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m36000909b5" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="mbfdc48c1bc" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mbfdc48c1bc" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p6f2237a0c0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Far-Distance Cell (Neuron 8) -->
    <g transform="translate(81.107844 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-46" d="M 588 4666 
L 3834 4666 
L 3834 3756 
L 1791 3756 
L 1791 2888 
L 3713 2888 
L 3713 1978 
L 1791 1978 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-46"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="62.435547"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="129.916016"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="179.232422"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="220.736328"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="303.744141"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="338.021484"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="397.542969"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="445.345703"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="512.826172"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="584.017578"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="643.294922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="711.117188"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="745.931641"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="819.320312"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="887.142578"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="921.419922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="955.697266"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="990.511719"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1036.214844"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1119.90625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1187.728516"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1258.919922"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1308.236328"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1376.9375"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1448.128906"/>
     <use xlink:href="#DejaVuSans-Bold-38" x="1482.943359"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1552.523438"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m1d05843a84" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p6f2237a0c0)">
     <use xlink:href="#m1d05843a84" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m1d05843a84" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="md52e0a7354" d="M 440.975667 -37.642188 
L 440.975667 -37.642188 
L 450.408971 -39.592268 
L 459.842275 -39.635127 
L 469.275579 -39.784283 
L 478.708883 -41.17175 
L 488.142187 -40.644481 
L 497.575491 -39.199258 
L 507.008795 -39.969456 
L 516.442099 -42.000415 
L 525.875403 -38.642033 
L 535.308707 -41.264364 
L 544.742011 -48.407914 
L 554.175315 -58.856412 
L 563.608619 -51.972987 
L 573.041923 -64.275825 
L 582.475227 -44.66887 
L 591.908531 -78.14797 
L 601.341835 -67.063714 
L 610.775139 -104.075614 
L 620.208443 -146.255239 
L 629.641747 -172.988616 
L 639.075051 -136.805721 
L 648.508355 -203.053144 
L 657.941658 -191.441274 
L 667.374962 -199.188183 
L 676.808266 -241.916554 
L 686.24157 -285.2738 
L 695.674874 -294.571711 
L 705.108178 -259.010738 
L 714.541482 -172.806074 
L 714.541482 -37.642188 
L 714.541482 -37.642188 
L 705.108178 -37.642188 
L 695.674874 -37.642188 
L 686.24157 -37.642188 
L 676.808266 -37.642188 
L 667.374962 -37.642188 
L 657.941658 -37.642188 
L 648.508355 -37.642188 
L 639.075051 -37.642188 
L 629.641747 -37.642188 
L 620.208443 -37.642188 
L 610.775139 -37.642188 
L 601.341835 -37.642188 
L 591.908531 -37.642188 
L 582.475227 -37.642188 
L 573.041923 -37.642188 
L 563.608619 -37.642188 
L 554.175315 -37.642188 
L 544.742011 -37.642188 
L 535.308707 -37.642188 
L 525.875403 -37.642188 
L 516.442099 -37.642188 
L 507.008795 -37.642188 
L 497.575491 -37.642188 
L 488.142187 -37.642188 
L 478.708883 -37.642188 
L 469.275579 -37.642188 
L 459.842275 -37.642188 
L 450.408971 -37.642188 
L 440.975667 -37.642188 
z
" style="stroke: #33a02c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pe933a91046)">
     <use xlink:href="#md52e0a7354" x="0" y="352.267438" style="fill: #33a02c; fill-opacity: 0.3; stroke: #33a02c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m36000909b5" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m36000909b5" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m36000909b5" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m36000909b5" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m36000909b5" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m36000909b5" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m36000909b5" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m36000909b5" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m36000909b5" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 264.233542 
L 813.937288 264.233542 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="436.259015" y="264.233542" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.1 -->
      <g transform="translate(414.946203 267.652839) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 213.841834 
L 813.937288 213.841834 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="436.259015" y="213.841834" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.2 -->
      <g transform="translate(414.946203 217.261131) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 163.450127 
L 813.937288 163.450127 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="436.259015" y="163.450127" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.3 -->
      <g transform="translate(414.946203 166.869423) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 113.058419 
L 813.937288 113.058419 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="436.259015" y="113.058419" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.4 -->
      <g transform="translate(414.946203 116.477716) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 62.666711 
L 813.937288 62.666711 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#mbfdc48c1bc" x="436.259015" y="62.666711" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.5 -->
      <g transform="translate(414.946203 66.086008) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_42">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_67">
    <path d="M 440.975667 314.62525 
L 450.408971 312.675169 
L 459.842275 312.63231 
L 469.275579 312.483155 
L 478.708883 311.095687 
L 488.142187 311.622957 
L 497.575491 313.06818 
L 507.008795 312.297981 
L 516.442099 310.267023 
L 525.875403 313.625404 
L 535.308707 311.003074 
L 544.742011 303.859523 
L 554.175315 293.411026 
L 563.608619 300.294451 
L 573.041923 287.991612 
L 582.475227 307.598568 
L 591.908531 274.119468 
L 601.341835 285.203723 
L 610.775139 248.191823 
L 620.208443 206.012199 
L 629.641747 179.278821 
L 639.075051 215.461716 
L 648.508355 149.214293 
L 657.941658 160.826163 
L 667.374962 153.079255 
L 676.808266 110.350884 
L 686.24157 66.993638 
L 695.674874 57.695726 
L 705.108178 93.2567 
L 714.541482 179.461364 
" clip-path="url(#pe933a91046)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_68">
    <path d="M 695.674874 314.62525 
L 695.674874 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #33a02c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_69">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_70">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pe933a91046)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_43">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 5.5m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-35" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-35" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.510 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-35" x="900.056641"/>
     <use xlink:href="#DejaVuSans-31" x="963.679688"/>
     <use xlink:href="#DejaVuSans-30" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.602 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-36" x="561.255859"/>
     <use xlink:href="#DejaVuSans-30" x="624.878906"/>
     <use xlink:href="#DejaVuSans-32" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.03 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-30" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-33" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.756 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-37" x="989.607422"/>
     <use xlink:href="#DejaVuSans-35" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-36" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.8m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-38" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_44">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 5.5m, Sparsity: 0.602 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-36" d="M 2316 2303 
Q 2000 2303 1842 2098 
Q 1684 1894 1684 1484 
Q 1684 1075 1842 870 
Q 2000 666 2316 666 
Q 2634 666 2792 870 
Q 2950 1075 2950 1484 
Q 2950 1894 2792 2098 
Q 2634 2303 2316 2303 
z
M 3803 4544 
L 3803 3681 
Q 3506 3822 3243 3889 
Q 2981 3956 2731 3956 
Q 2194 3956 1894 3657 
Q 1594 3359 1544 2772 
Q 1750 2925 1990 3001 
Q 2231 3078 2516 3078 
Q 3231 3078 3670 2659 
Q 4109 2241 4109 1563 
Q 4109 813 3618 361 
Q 3128 -91 2303 -91 
Q 1394 -91 895 523 
Q 397 1138 397 2266 
Q 397 3422 980 4083 
Q 1563 4744 2578 4744 
Q 2900 4744 3203 4694 
Q 3506 4644 3803 4544 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-36" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m73552118d8" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pe933a91046)">
     <use xlink:href="#m73552118d8" x="695.674874" y="57.695726" style="fill: #33a02c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 441.859015 86.47675 
L 558.766515 86.47675 
Q 560.366515 86.47675 560.366515 84.87675 
L 560.366515 50.44925 
Q 560.366515 48.84925 558.766515 48.84925 
L 441.859015 48.84925 
Q 440.259015 48.84925 440.259015 50.44925 
L 440.259015 84.87675 
Q 440.259015 86.47675 441.859015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_71">
     <path d="M 443.459015 55.328 
L 451.459015 55.328 
L 459.459015 55.328 
" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_45">
     <!-- Tuning Curve -->
     <g transform="translate(465.859015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_72">
     <path d="M 443.459015 67.0705 
L 451.459015 67.0705 
L 459.459015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_46">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(465.859015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_73">
     <path d="M 443.459015 78.813 
L 451.459015 78.813 
L 459.459015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(465.859015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image7bbf41d2fe" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_16">
     <g id="line2d_74">
      <defs>
       <path id="m96ef997b2a" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m96ef997b2a" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_75">
      <g>
       <use xlink:href="#m96ef997b2a" x="341.56486" y="247.683359" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.1 -->
      <g transform="translate(348.56486 251.102656) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_76">
      <g>
       <use xlink:href="#m96ef997b2a" x="341.56486" y="207.719067" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.2 -->
      <g transform="translate(348.56486 211.138364) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_77">
      <g>
       <use xlink:href="#m96ef997b2a" x="341.56486" y="167.754776" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.3 -->
      <g transform="translate(348.56486 171.174073) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_78">
      <g>
       <use xlink:href="#m96ef997b2a" x="341.56486" y="127.790485" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.4 -->
      <g transform="translate(348.56486 131.209782) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_79">
      <g>
       <use xlink:href="#m96ef997b2a" x="341.56486" y="87.826193" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.5 -->
      <g transform="translate(348.56486 91.24549) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_54">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p6f2237a0c0">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pe933a91046">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
