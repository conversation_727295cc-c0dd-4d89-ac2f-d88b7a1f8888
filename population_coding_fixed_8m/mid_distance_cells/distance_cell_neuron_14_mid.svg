<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:39.597770</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#pf2f075b8a4)">
    <image xlink:href="data:image/png;base64,
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" id="image1c1f387652" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#pf2f075b8a4)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#pf2f075b8a4)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#pf2f075b8a4)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m9cdc8b302a" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m9cdc8b302a" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m9cdc8b302a" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m9cdc8b302a" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m9cdc8b302a" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m9cdc8b302a" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m9cdc8b302a" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m9cdc8b302a" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m9cdc8b302a" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m9cdc8b302a" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m8f5e33b78b" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m8f5e33b78b" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m8f5e33b78b" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m8f5e33b78b" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m8f5e33b78b" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m8f5e33b78b" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m8f5e33b78b" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m8f5e33b78b" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m8f5e33b78b" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#pf2f075b8a4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m8f5e33b78b" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Mid-Distance Cell (Neuron 14) -->
    <g transform="translate(75.364719 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-64" d="M 2919 2988 
L 2919 4863 
L 4044 4863 
L 4044 0 
L 2919 0 
L 2919 506 
Q 2688 197 2409 53 
Q 2131 -91 1766 -91 
Q 1119 -91 703 423 
Q 288 938 288 1747 
Q 288 2556 703 3070 
Q 1119 3584 1766 3584 
Q 2128 3584 2408 3439 
Q 2688 3294 2919 2988 
z
M 2181 722 
Q 2541 722 2730 984 
Q 2919 1247 2919 1747 
Q 2919 2247 2730 2509 
Q 2541 2772 2181 2772 
Q 1825 2772 1636 2509 
Q 1447 2247 1447 1747 
Q 1447 1247 1636 984 
Q 1825 722 2181 722 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-4d"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="99.511719"/>
     <use xlink:href="#DejaVuSans-Bold-64" x="133.789062"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="205.371094"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="246.875"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="329.882812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="364.160156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="423.681641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.484375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="538.964844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="610.15625"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="669.433594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="737.255859"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="772.070312"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="845.458984"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="913.28125"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="947.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="981.835938"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1016.650391"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1062.353516"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1146.044922"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1213.867188"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1285.058594"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1334.375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1403.076172"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1474.267578"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="1509.082031"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1578.662109"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1648.242188"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m3e359abf4a" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pf2f075b8a4)">
     <use xlink:href="#m3e359abf4a" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m3e359abf4a" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m5e046fc6ca" d="M 440.975667 -37.642188 
L 440.975667 -37.642188 
L 450.408971 -50.046126 
L 459.842275 -53.402683 
L 469.275579 -53.393485 
L 478.708883 -61.384886 
L 488.142187 -58.945106 
L 497.575491 -61.219053 
L 507.008795 -58.59065 
L 516.442099 -78.922871 
L 525.875403 -70.960859 
L 535.308707 -75.856659 
L 544.742011 -121.239462 
L 554.175315 -149.743979 
L 563.608619 -175.158213 
L 573.041923 -175.98601 
L 582.475227 -144.045843 
L 591.908531 -216.732382 
L 601.341835 -169.076331 
L 610.775139 -207.071907 
L 620.208443 -276.764396 
L 629.641747 -294.571711 
L 639.075051 -207.689208 
L 648.508355 -268.080541 
L 657.941658 -241.411459 
L 667.374962 -201.621221 
L 676.808266 -217.9489 
L 686.24157 -154.241346 
L 695.674874 -195.822098 
L 705.108178 -140.514878 
L 714.541482 -72.698828 
L 714.541482 -37.642188 
L 714.541482 -37.642188 
L 705.108178 -37.642188 
L 695.674874 -37.642188 
L 686.24157 -37.642188 
L 676.808266 -37.642188 
L 667.374962 -37.642188 
L 657.941658 -37.642188 
L 648.508355 -37.642188 
L 639.075051 -37.642188 
L 629.641747 -37.642188 
L 620.208443 -37.642188 
L 610.775139 -37.642188 
L 601.341835 -37.642188 
L 591.908531 -37.642188 
L 582.475227 -37.642188 
L 573.041923 -37.642188 
L 563.608619 -37.642188 
L 554.175315 -37.642188 
L 544.742011 -37.642188 
L 535.308707 -37.642188 
L 525.875403 -37.642188 
L 516.442099 -37.642188 
L 507.008795 -37.642188 
L 497.575491 -37.642188 
L 488.142187 -37.642188 
L 478.708883 -37.642188 
L 469.275579 -37.642188 
L 459.842275 -37.642188 
L 450.408971 -37.642188 
L 440.975667 -37.642188 
z
" style="stroke: #1f78b4; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#p1f34babb68)">
     <use xlink:href="#m5e046fc6ca" x="0" y="352.267438" style="fill: #1f78b4; fill-opacity: 0.3; stroke: #1f78b4; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m9cdc8b302a" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m9cdc8b302a" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m9cdc8b302a" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m9cdc8b302a" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m9cdc8b302a" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m9cdc8b302a" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m9cdc8b302a" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m9cdc8b302a" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m9cdc8b302a" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m8f5e33b78b" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 259.056633 
L 813.937288 259.056633 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m8f5e33b78b" x="436.259015" y="259.056633" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.2 -->
      <g transform="translate(414.946203 262.47593) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 203.488015 
L 813.937288 203.488015 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m8f5e33b78b" x="436.259015" y="203.488015" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.4 -->
      <g transform="translate(414.946203 206.907312) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 147.919398 
L 813.937288 147.919398 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m8f5e33b78b" x="436.259015" y="147.919398" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.6 -->
      <g transform="translate(414.946203 151.338695) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 92.350781 
L 813.937288 92.350781 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m8f5e33b78b" x="436.259015" y="92.350781" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.8 -->
      <g transform="translate(414.946203 95.770077) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_41">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_65">
    <path d="M 440.975667 314.62525 
L 450.408971 302.221312 
L 459.842275 298.864754 
L 469.275579 298.873953 
L 478.708883 290.882552 
L 488.142187 293.322332 
L 497.575491 291.048385 
L 507.008795 293.676787 
L 516.442099 273.344566 
L 525.875403 281.306578 
L 535.308707 276.410779 
L 544.742011 231.027975 
L 554.175315 202.523458 
L 563.608619 177.109225 
L 573.041923 176.281428 
L 582.475227 208.221594 
L 591.908531 135.535056 
L 601.341835 183.191106 
L 610.775139 145.195531 
L 620.208443 75.503041 
L 629.641747 57.695726 
L 639.075051 144.578229 
L 648.508355 84.186896 
L 657.941658 110.855978 
L 667.374962 150.646216 
L 676.808266 134.318537 
L 686.24157 198.026092 
L 695.674874 156.44534 
L 705.108178 211.752559 
L 714.541482 279.568609 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_66">
    <path d="M 629.641747 314.62525 
L 629.641747 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #1f78b4; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_67">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_68">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#p1f34babb68)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_42">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 4.1m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-34" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-31" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.925 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-39" x="900.056641"/>
     <use xlink:href="#DejaVuSans-32" x="963.679688"/>
     <use xlink:href="#DejaVuSans-35" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.359 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-33" x="561.255859"/>
     <use xlink:href="#DejaVuSans-35" x="624.878906"/>
     <use xlink:href="#DejaVuSans-39" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.00 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-30" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-30" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.391 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-33" x="989.607422"/>
     <use xlink:href="#DejaVuSans-39" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-31" x="1116.853516"/>
    </g>
    <!-- Peak Width: 2.8m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-32" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-38" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_43">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 4.1m, Sparsity: 0.359 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m2bcabd563d" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p1f34babb68)">
     <use xlink:href="#m2bcabd563d" x="629.641747" y="57.695726" style="fill: #1f78b4; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 691.429788 86.47675 
L 808.337288 86.47675 
Q 809.937288 86.47675 809.937288 84.87675 
L 809.937288 50.44925 
Q 809.937288 48.84925 808.337288 48.84925 
L 691.429788 48.84925 
Q 689.829788 48.84925 689.829788 50.44925 
L 689.829788 84.87675 
Q 689.829788 86.47675 691.429788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_69">
     <path d="M 693.029788 55.328 
L 701.029788 55.328 
L 709.029788 55.328 
" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_44">
     <!-- Tuning Curve -->
     <g transform="translate(715.429788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_70">
     <path d="M 693.029788 67.0705 
L 701.029788 67.0705 
L 709.029788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_45">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(715.429788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_71">
     <path d="M 693.029788 78.813 
L 701.029788 78.813 
L 709.029788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_46">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(715.429788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image24b4ae29da" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_15">
     <g id="line2d_72">
      <defs>
       <path id="m9115b642dd" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m9115b642dd" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_47">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_73">
      <g>
       <use xlink:href="#m9115b642dd" x="341.56486" y="242.398278" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 0.2 -->
      <g transform="translate(348.56486 245.817575) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_74">
      <g>
       <use xlink:href="#m9115b642dd" x="341.56486" y="197.148906" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.4 -->
      <g transform="translate(348.56486 200.568203) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_75">
      <g>
       <use xlink:href="#m9115b642dd" x="341.56486" y="151.899535" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.6 -->
      <g transform="translate(348.56486 155.318831) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_76">
      <g>
       <use xlink:href="#m9115b642dd" x="341.56486" y="106.650163" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.8 -->
      <g transform="translate(348.56486 110.06946) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_52">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pf2f075b8a4">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="p1f34babb68">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
