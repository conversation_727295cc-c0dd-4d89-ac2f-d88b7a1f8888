<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="828.512413pt" height="352.267438pt" viewBox="0 0 828.512413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:41.645138</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 828.512413 352.267438 
L 828.512413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p4e4cb95f1a)">
    <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAjMAAAIzCAYAAAD1UpjCAADP/0lEQVR4nO394XajSrKujb4usJBke1at7rlXr2+ffa/nbvf5Vo/uXjXbnrKQwXV+JAGRQWSSyHKVVfU+HhogQICQRT6KjMy8Af6/30AIIYQQcqV8+tEnQAghhBDyFigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWooM4QQQgi5aigzhBBCCLlqKDOEEEIIuWrqH30CPy+377DPl3fYJyGEEHLdUGYuwnuIS8lxKDeEEEIIZeZsvpfA5NDnQLEhhBDya0KZWc1HkBgPig0hhJBfE8pMMR9VYjzkXCk1hBBCfn7YmqmIaxIZzbWeNyGEEFIOIzOLXEII7GXuLrDPUhilIYQQ8nNDmcmyVmRKL2fpdpeUnltQaAghhPyMUGZcSiXmvS/fpSM6FBpCCCE/H5SZGSUi86Mumz7uuWJDoSGEEPJzQZmJWBKZcy7Xe3V0J+dyjtT8KkLzlnynX+H6EELIzwFlZuQSIlNSeOa2OacArUGh0VyqBRd7WyaEkGuBMgPgfJG5dNPnczu+e4vQrD3WR+W9m6H/TNeKEEJ+LtjPzIcRGW//a45R43w3veb+aNZep2s7HiGEkCUYmcniXZ63tnRaG0FZGxH4laqd3iIVb20pdo3XixBCfk5+cZnJFYZrRebcPmZKC9E1UnOu0FwTa6NWa7ZZ85lQaAgh5Efzi8tMilKRucTlW1uIlhag5wjNNRTO36MPoDXCyVwaQgj50VBmZlxaZNZGVIBlCXlvoUHh/r8nl2o2f05rshK54ajlhBDyo/iFZeaS+RYl+13T1LdEat5TaGT/KDzGe/I9E7RLRWcpmvZRrh0hhPwa/MIy41ESlXlLUrD3miWp+ZFCs+YYl+YciblEThOwLvKSE09KDSGEfA8oM1mWRGbN0AcpmViqnrhUMu9bhQb4foXyJRKz13ZymIq8CPraedcjd32vIReJEEKuF8rMyNKlWCMy3r5KEn1Thd6lCsq3itGPLpTfM3LmbbMUgbHXYylKQ6EhhJD3gDKTJFcAvrVl06ULve8tNFhxvHP3rzlHYs6tcvIiMMD0fu1ntyZKQ6EhhJD34BftAXhtjosu/LxCdCkhOHW8Ncmrl/TOS+zrPYZyuITIePupzWNpX962dr8l+yGEEPI94B0XwPmXYY2MpCIaqSiN9yv+knkZbxl1+9xj5vZz7rYp0TznsxFyVUf6PefWyfqSz5UQQshb+EUjM0ukCsWlwrJk3J63RCCWOCda8laffWuEZk110Dkic2seeptctCa1PRLrSs4xtQ0hhJC3wMjMRSgpkM+NvKxJMk3td4m3jiO1lDxbsr3mHIl5q1CsvW6d2scL4s/FRuFSycN6G0IIIedCmTmLXMHpbaOfn9PE95wqJ8s5heZbBmM8N/pQGgnLSUxJMvCaHoNLr12J1Hifrd6GEELIWigzxZTkWgBlTbxLoi8l4rAm7yUlF+fk2ZQe85z9CkvVOGuiM6VVPedEtURgliI1OaE599iEEEIAyswZ5C5ZrtA8p8+S3HrvuG8ZtsCyVLC+VWyWWoDZbTw5OUdsvOdLrBENT2o8oZFtzz0OIYQQgTJzNmvzMkqqit4iNPocLhE1ybXwSR1XWDp+SUujtdGY1HM7nzv+JdEC4wkNkI7SEEIIWQNlZkZJ815LaYdtJVGatwqNdz6Wt0Zw1h6/Syz39q23K4nGpJ6v6RPGrlsbhfGEZem5Pre1ny8hhBANZaaISzQfXiqwdKFWUuDB2UcpJR/70gCXa46/ps+Xt4rMua2b1vDi7FMoERhGaAgh5JJQZlbhFbSaXKFZIiQ5oVk63qV/zZdUHb3l+CXJ0+dGY3JClDq2Pbe170fnynhCA5R/vozOEELIGigzb2Ltr/+1ib8lrWDs8d+rECzp30ZYI2F63976c6Ixa1o3vVUcdJQmJzTAXG7WfL6EEEJSUGbeBa9wXpMnkSvwgDKpuSRejo+wFLHJsdR3jH6ei8bktrfH8ZZfsrm8h+zHi9akZIrRGUIIKeUXlZlczkNu3TnoQndNHowt8Oy6ayKVALyU+JuSGG9ZSaumpeba54yblZMOHZWR/eSE5tK5UYQQ8mtwbaXileMJDZCO0ngtnTzeMnTAW9DVKh6efOWqmJYkZUlyzqlm8s7lXHnx0K+TfafkBYlljNIQQkgOyszFWUo0LYkApCIxa4YvWDq3UlLCYqMO9jVIvM6ex1KrpZzErBUa75y8z6O06mdN1ZNNAl5q8bR0bEIIIQJlBkAoTLxLkVpuWRv9SFUflErNW3nPaE1OcrzjpyIzNifmLULjHVe/JhUNAdZFZErkZk0fNBYKDSGEeFBmijgnj6Zk+5zUnJM7seYccx+9d2xNSlhy1U72Gi5VL1lhWSs0uWqlVMRMzlNTUvW0llQVkz7mJUdF9/ZRCuWJEPLxocy4vFcScK5fEZhtUvk0azj34z3ndUvVTmsSc210ZY3QpFo0ecfyKG0xlivkl6IzXg6OFZhLCc1b/49TMksIIR8HyswqSqudNLeJ+SWx8aqWbGG39viXwJOWXF4NMJcVmOX2Gml58ZblqqPsvHdce/w1o1inJEOWl/yPpPJn7HHPaQ7+nlWI9hiUGkLIx4Ayc1HWXM6lX7xegZHb/1Ih9j0KOU2uyslKjBWcJXnxEoOBudDodZYbNS/bfkucp7yXJZHJHQ/ONrkegr19a6wA/QgoNYSQj8EvLDOXrkrSnLtfr3Cw0ZxzpeUjfdRelZBelxKapdZN+vVALCwlpLa/xVx0Unji4UmM7LckwTgnNB8BSg0h5MfykUq4H4ytHijpWE9eY58Lb5Ua75hL+/U+0lQrnnNIRSaWjultn6pyKpUYLwqzVmDWkLpuS30HWYmRZWtyYs6pcrKc+7mXHrekGpUQQi4PZWaRc/JkUiK0dj+55N9zpOUSUuMVbDv4hVeutY49n6X8mFzV03sKjHCDeXSmJE/IfoZeE+ylqia9nd1naptLkmq5lsNeG8oNIeT9oMwUk4q+lHLp15TIUq7F0JpjaZYK2jXbLsmMfa6nCwLzFm9McmN2nKv6SUmNlZVU9dJStZPs8y0J6TlK+tQRSi4eq6IIIe8HZSZiTeGwJufmLYm7pfvNRV2WWhClXueRKri86EyJ+KTyZs6QmNqZnlMzk32NFZrUiz2p8XKhcmKzJADv9X+Ve32qhZewplk6IYRchl9cZi6RBHxupOac454TeclFQVLnkTu3XEFkz2+NzOj5FRLjCYw3zZ1OjtWvsZGYWzO/ZkwmoTSqcQm5RuY87DFyYlOyD0oNIeQy/OIy46HlZE3V0hoxekvuylsFJpVEvJRfI6TyY2SdLcRSVSjecT2ZcSSmRl5aUpczVc52Zl1qfiQXnfEiMXrei0zkcmjsdoD/eo9zv96lVYY5KaHUEEK+H5SZVaRaMa3hXInwXlMiBfp5anluH7nje3gRB0vJeWckRsuM3kVKaM6NyCyWx/I/oDfUBbSNyqTGf7pkx3glX+ml/7G1VUlLUsOqJ0LI+0KZcTm36mgtJfKQk4G3CMxSVAdI56dcUmYKjmklJicv3uFseZoqX72PPRud+Ya4ILYRmM4ss/Opa7U0dpNH6jM5pzrz3KqklJSURmkoNISQ86DMfNe8mZJE3JJCaa3ELAnNpTqX0/sWvPdTcDwtJymZsbv35ktaPFtyH2e2ukn/L3l9EAHpKIZXmJeGl9ZG99bIaK6JtScpb4nSUGgIIedBmUmS6hBvKY+mRGxS4QPNUvPptRJzhsCc+98xK/BXHicVhfFkJvX6pfPT5Wptlq0NiryZkgMuvalz87BsFGYpX8aTlTVSUyI03usIISQNZebNlPQUnGKpyseu816jJcaLvCzkodjDLOWflFIqAymvsxKj51PnsxRweBdp6RA+Z72jF7XOcslC+tycJ4tcCO81JQOLloz0nhpjbE0vyIQQ4kOZAXC5cZreup9UVZJedyGJSclLTmq857IsJwUlaTR2/drzWsPFxKZ0vKa1SEG+JhlcXuctL8ETFrvPlLRYYfFkJRep0dtplnJ3CCEkQJlZRWlV0xq83JmlAuoWaXEpbBHkzaeWlc4Lb4l2ePu+hMAAfrPrNd29FL0vr9BdUxDnoiSy3lKSi/VWvGjMktSgcBvZjtVPMZf6gUXIzw9lZuQt1UXAXGxKq5gsOblJRWO8eSMyueqaknkk5s/9DyodFWFtdddSKyWvdVJJZKlIZLyqJm/92gKmVGByy9ewVlxsJCYlNbnejvX5/spSc2kJZXSL/BpQZrKURl7eOrTBmubTnrys7J+lRGzOrYZ6D5aOsbbZdWr71HwRqYJizU6WWjTJNt76kvyrEpbkZElc9IVLVT+9NUojr/2ZCudLS8zSMX6ma0cIZcawJgKTq2oqlSAv7HBOK6WVEuOJy9IyZJal3lKOS7UWWhNp0eu9ZWedk03+hfN8LXIyJRGX1P+LnT+HXIeAyKzzpOWcKM3PLjTfQ2CWjn3N14+QCcpMMW9J7s3JjVf4vLPIrBGcpeqn96puKllfkgNTss+zRWaptVJqvoScsOhlern3/3IJSqTGE5fS19jXreEaheZHSozlGq8fIXMoMzPWSIu3bcnrc01q3ygyWi62eB+hST23pJYvBTJKW0Z1anq2kDivK9rXpVoyLRUmOZFZkpi1hWZKWpbWv0V2ckJjIzwprqX65CNJjIZCQ64fyoyL15OrZk2HeaVy5EVocs2uEyKz9FgSnJL9eNvY80g992Rk7Tp53pn52pnP7cNSWnYm0QVC7s2kDi4ioIdE0Ce2RmI8oTmnwHqr1KyJ3th1UNuUfijedy31vnPfy/fsD+itlP5KWAOFhlw3lJlVrG3xtLZqyv76toXTGyTm3EhNqeTY00/NA2USU7JMP1LLNNth+dFZ553Xd0cLjTxH5nkqaifrlizTvuFcgXaO1FwiemPP/ZwP6RyZ+NFRlHNuzUuf7xLMoyHXC2XmbFLtfb31lpzk3Jp1K0SmRFbeIjSlwgMz712GVODCiktuWvqw8nLOf/1iuXBOIetFY2SZJzIlETuYdTkJOKfwWiM19jVrqp1SYrUmSnNtXPp2fK4AMkpDrg/KTJLSqMqaiIzXQ2oKLTAJkfHEZElW3iozudfDmc+93ZzQWGlJzR+xLDFbxEKjz6f0Pj8rQ9cOzrmEF8Gw0pLLn/Lm9X5z4nKOIJRKTS5K86sLzfe6/Z4jNRQacl1QZorQv5Rz+TSerKTGpElhBcYRmZxg5GTjXJGxr/Oe6/PKzZdWMeWqjrSs1PDF5oi4WkkLjT6XpaiR5l3KULvTVCulVLUjCubPSaotJSc1VmjgLPvVhOZH3nLXfvasdiLXA2XmXclVNXl40RwnIrMkMZcUmpJjWJnJRWuAfG5sKspi12mROTrbwGyHzNSeg/3IVpeZSzf/EsHwRMZLAl8SGh3R0cf0zmHpjdpO8zRaUnJ5ZZ7kLInLtQvNubfZc1qjlbD2ujFKQz4+lJmz8G7YXpSm5GakCybBFlQKkQhPLLx1l5KZ3DG8fcpbq78BdQfUffw+ukrNq/fe3ZTlwWiRqTFFXWwkpjbzUK/V0+jczPPvVmbaE7Ei40VmchJj9+dFgUoKKSsxXtWR9xqvmqlUaErP9dwP56Pe+t6SeLwmkkKhIT8XH/Ub/QHJRVmsuKyNyGhsgVVQvfQeMuNVJRUdb5KXT3WPqu5R1R1qIzOdlhkA/SA0fVfhtauC7HT1JDc2N0aLiK4+knkRGpm3UpOLznwXedH/L6loRKrKMReh8aQHSEc/zhUcTU5UzhGaFDmhuWYu3XKqVGooNOTn4drvAu9MSVPspYiMV3Ug2H3b5E0jMqVRmbfITCqp2Nv/OB8E5tP2FMlLJY+qR4N2uBqTxPTmevR9WHc6btB1FfquDnJz3ADbQWxEaqzE6OeClhj9XB5edEY+Lu/5uwmO9z+WExkvUpPLqZFlqc7q3pNzhMbLsxF+lgL10gKTO0buelFoyM8BZWYVSxGXc6uaPOp4NicduajJGpnJvWbrHGf7Ddi2+FT32GzbSGA2VYsaPSr02CDMT1fJF5q+Csvbuw161Oj7KojNtsXp2ISIzbYCjrfx+aX6jRGsxHhCA2ceapti3ksSUkKzlEcjQvwN8Zs5V27OkZG3RmguET36CKy9Fyzdntck8lJoyM8NZeZNXEhW3ALKicrYiEju+ZpoTInkzI4RROZ2e0KzbbMSI/PCLCITyU2FDVqc0KCvKmzuWpz6Bs32hPa4Qd/VeKl7oG6A+iY+z7V0iFs5peZrs2y878tnry3oLdUgeruliMxebZ+TGOFGbQtMciPnL/uw8/KaFzNvJcRjSWhyvEd1mHeMj8aaf2S97blVdHpf15BMTYgPZeZirMmbseF/mUohol6XjIosLF+Sl7WSEx3vBaj7UWQ229NYlVQN8iLzWmiqITqj5cXSo0KHCiec0KNCiwZNdUJbbbBpTji1G/TbFu3xhJenHVDfzr3QQz4O+95ss21v3hOb8b5fA9ipA3kFZElBY7ECs1PzuSqn0v5v9Ha3mMaasuLiSc2agi8lNN7+lvbvCY3e77Vxydvv95YRRmfIx4Iys8glk3st+pc11PzNtNpGR1Iyk4vMrJGXRZl5waftaaxW2mxP2FQtGpwGYenG+UloTqjQQaqauozMAMAJDRqc0GKDDU44YTNGa5qmRds0qOoeByBEacakmAGvubfZZFxuoy6pedsiajzGjbpYqYMv/b/Y9fL/oAVGS02hxMhp5ZrCj9xgkhp5kSc1qeopPW9bOXlCo7dLCYxXYKa2O4f3uv3ZxKvvxZLQsLqJ/LxQZi7CmlwZKzC1mqpCMRVpyYnM2shMqcyMQjOJTLM9YdOcRnmRCIyIiwiNXi5shmnl3Dh71DORqdChR40aPTpUQZqa8Nq27oJ31A3cAt2KjI7Q6MRgYLmaSUdzovu+iIDeGInnFvt/ov8/cgKzQzIKo93KCs2i3NyoDT2pObfwskLjnYw+hpYn/Rp5HZzXpvgRUnEJrBCWHpsRGvLrQZk5i0tFZ26daSYqswVwDyMYicdFZWZqraRFpkEbyUssNP1svaaC6XdGEUVi0GIz7L1BG6qdcEKFPdAA1dDk+wgAnbKWzkxl3hOb0lwZLTKz/JlcZEZjRVf/H9nlIjFedVMmEuOJjD69XEBlREuNnNuaTva0ANlldn8l1VclUZpzvpOXrJ4qKdTPPV5Jy6QfBYWG/HgoM2/mnPp6W4CZKgM3KrLysSQpKNimRpToW9XdTGTmQuPLjU4Arh2R0fk0k8iECI2IUYtmjNaM+6r6IHgQB9lOfdEAfhRCS4wsK82VsdcvWd0k2Ju8Nouc1Mj/hM2XMSKTOi/7XB86JzeAIzU2p2bNr/9UlYuNznhyY8lFaUp477ya75G3UyI1a6Ww9LVL50WhIT8OykwRJT3+Li232IJPFVKpqEzJ4968/uzHUHipFks60VeLzCQ082iMrNcJwLZ6ycpNO8uZsdVOsp8eB+zQVRX6beiX5qWrgO52LjMSldHRmVxrppKojI58AIirmwSvGslWSXlS41Uv3cab5UTGzi+JjEyLpca+p6UCMDdMQSpUlIp2lRScpWJxqVvg924J9BHl4SOeE/lVoMy8O3KzTt1cb+frpMBcIzT35vk5URiRGNODr4iMJPrqiIvIywZTEnBqmZYQIZ4PBYJN+q3QRfkzIksV+kGRnoEm7OMAuZ0O19STGpnq65uLyuREZlbe6q+UlWB5biXGPl/Ij1mKtNl5eZ6qYvKWZaVGn++5v+SBdOsmO+8dw4tQlAjMe93y3mO/b2lu/SOiMyX7JuR9oMy8iTWJv0sMr/OiMjmpsRKzFJmxx8hIjPTka0XGq0KK18VVTXpZOHxeauohcfiEJprXUiNEfdQ0ocfgMCRCjWicp21iuqaaybt+s9wZXdin8mdyEiPLMyKTk1VvXg69JjJjnxdHaTxShVtJyyhklsk+cizd4i5RNfRehbc+93PF5EfwEc+J/OxQZr4rt87DlEqeaNiIS8nzJYGJlqVFRoYjkL5i9NRGX6b+ZGKRkWV1VNUUVznZqqZKSYqe74ftWmzGc2gH0WnQ4lRvUNU9XusOYx80Ii6pa9thkhc7D8TliC1TZFuZBzAJjeS6eEmqNgqjl2mZUSJjxdaTFu/ztQJj34ueWoFJLYveZworb7n1S9EZeUPe61PrNTlpOec2qI99rhCtKfBTgizHXzsIZ0l0JnU8Qj4elJmL4t3Ucjc6LTVOvkyq8F3aJicysMvmIqOp6n6s0vGaUo/bjdv4yb1LImOrm3pUqNEPf9X4OpmP91d4w81dIxuZsQW4bDsKi9qnXja+TqIXNYBnxEm88kIgLTNOtZInM3p9aj4lY3a+RGJE9rJ5QoJXWHpy4vU9Y+e9fdjb11J3CCneK3G3pJrI43v11FsSQTn3eIzOkO8LZebiLF1SzyycVSWPkpZLSMxnTlNHZWbr0GfFJRxiit54rw/TucjIVEtMOJaITT2TKv2acO7mmEvXQF8HLTHykHu5lRjZj12uC3wAobDfmZ3pE9My4zS3TkVjSmVGT/U5es+XJCa1PHofz2rHtrD2cohku1R1k3eia6Ms7xGVye0zJVvefjzO6V8mxZKMUGjIzwFl5rthS87CKiYtLbZaqSRyYw8dRWfy1UsAZlVM83fVzyIvGi+ikxIZWV4piZHegmW9jtIUkxMZHb3IPey+clGazsxHURpbUGc6vrOfZanM2GUentDkBCYlM9kojT6IFhnbB01n1tVmO6HkduVJRup17xGRyYnO0vsprULy5OLcZOBSLhkRIuTyUGbehVzzXNt6aaGKKVegLW2b+oVeYxKZDKnIjM6Nma/3q6Qq85qUyAgSnfGqmmwCsX1e1Z1/W/euF5DONbURGk9g9GtkXTKCITkmhT33Ln32W7Wt9/q1gYESgTma+VkCdKrvHNnAVinJdimhWcuayMylZSZ13qmIUkpuSkTlknJRGkE555iMzpDvA2XmTeQuX411UqNetkZWliI18KaqFUoq6XdI/NVykou8pNelozn69bIcwExebFWTLA/TzM11KRpjC3wtMXoeSEuMvO5o5lMCYEUmF0FbI7Le6/V0iZLqpM5sDySExhbKUsiLyNjoVGr4gnNvT0t9/ehjryWXx5OSlVQEJlWdtDbyUrLNkoh4Td3P2c9b9k3I+VBm3h25xN4vVlX6LMlLrol2KuJgDwVMIjNUL61lTaLvdApTVEZXVaUjNd1Y1WRH2JZ1Mq/35ZK699prZiUGmBfgqdfbBODUVMQmJyBvlRlvPyWsqWKCmZf3NC5PDb6pLzIwr1LSF3/NL/oleVkTrTkHT3BS+UH6fPRFtAV+aps1Q0qcw3tGUhilIe8HZea7Y0uZwlZMJQVaKtoAII7GTDfApahMLiemJJfGw8rQJDtxArCdVggDTco+9P7G+SojNblIBtS8JyL29alITeq1OnKxJDPnfPbI7LOUJZHR5++9/2T+jERlrOB4zbC13KwRjlT11iWjMqVYuUmJzXsIyyVyZ9ipHrk+KDPfhaVO0szqVCG2pln2uE9fYlJNsS25KIvdzqtu0svmfcl00CIjTa/nVVAbU9UUi5B7rnWPqCPClEjoPmjgzAu5aqbpZEyVEuZVNt45lArNUlXiGpmxy3NVSzZ3SCc5e4+ouklXLel52dAmAZ8rNCmB8S5Argn32kLaE5d6Yf1SFCZX4L9VBj6K0GBh/4SsgzLz3fBEpp5WXfIBpCUGgK1eyjXF/t5MQhNXMUlsSCq5PKLlnbON3H+9SIZN4wDOK9dkH0c1tfkl54hMTmjse/HmNalvfWlkpkNclentA8A8OqNFxsuT8YSm5MSB8shMah/6e3mpQjYnNimp8YQmJw7eukt1iPeeQiP7PxeKEImhzBSRC1WLlCxNvW0Lq5jgTD2ivA+VaNpJhGIQnC7s5LXu8Fr36OsKvapqEnpUQAU0OA3VO9MwAoFWNZ+e1jVDr7yaRvXU2w0RlmpUlylvJuzVDpwg0w2esUeLBs/Y4Rl7HIZHO6w/tRucjk14j16uR0pclr4JJaKhBQaIC3+o+ZJoypLApP5HlmQm9bxEZOShRUZHaPQxs9EZe1EELTJeHorFvpmcuKzpUC9X2J9bDVSS0Ky3seKTWp46h5K+auz5pN5vyT4unbuzxNL/BmXnV4MycxZLl01Li37oO776Mq75pe0d3uZVpu4rnW1JcwvUdVJqqrrHCQ2mgIcvL3rdaZAYT4BEYACMEhMEZjNoTVhqx+S2YvOM3SAwDQ7YjWNzn/oG7XGD164CjjdTVMTL+UiRWlciM1pkPKnR8yWfdUkzfO/c7LLUe0v9H3nXTZbr5uy561AUnfHQIrNmEMmUuHjHKY0IpAryUoFJkZKT3PqS6A2QFxK9fw99rVJy9NGkJkWJzJGfCcrMalI3xxqxsNjtrdzU0+KlX9ypwwJziVl9L7kJkZuM1GALzGt22iGyErJYtMT0qNGq7URobBQmyM+8nxkRmech4mLFRqIxz9gN6xocsMfpOERljo0flbGk5NC77iU5M/o1Xgsn2U+JyCxJzJLALP0P2flUXkxKZLZmmhWaGn50RuMJQsmPBm8/qfW5/eUKP+/cSquBStHSYiMyXpQmlV+TExJhTdTmnCqsjyI0AnN0fgUoM4vkfsV58gLE4qKfO6/JFUDnSM3SfcRWuYz7H5rTdrHUYNsCx01CaCaaYWRrYQPMhKZyIjPhNHR0poqqlU4iKup5q4a4DGKzw6ndoD02eD1u5lEZ+77lvdv51NReq5wsWJHJHbtUaNaOx7UkM6n/J++aec9LRCb6X/SiMzneKjZ229TxlpZ7kaFL9sOSyxF6MeusvNTOPrxzscezLEVtPKkpidJ8NKEB2JLq54Yys4rcrz5Zb2+qVmpkWUG+jN31W0TGrnMLoZvhGJPUnABg26LqKvTVvNF0q3bZoI2WWaGRXBlAWh4FgTkhbon0jN2sWsl7PlYxDdVLL8cNcLwNhXLUTNi5lnbeioRdZnNDvH0uNde22y9JTe6xtmXbkrzp/yGbvOzJjRYZ2yLMFZra7ETIVaHk8D4I+5q3Nsv2vlT6fM+JUuRygWwkxgoNMJcXvb3eT+p89Hl4rKlay73/twhN6Wd0zv4ZpflZocxcBCsxnsBosannq5YKMu+QGpuG4OVp2Hn9uoTUvB4RhEaI83qxAVSuSzMu84Sminrw1VEZvX6KzEwRGRGXJnr+jD0OvalestUjuWuWk4jo+bcQ7bGvLfl8UpKTEhlv3+dKjGyfO54+ru25WKa6l189GGfqmJ2ZBzD1fJwzQiBd2CxFSDUlopPbPiVbOdb+6rcCYqM0ntDAWaeX6XPRnBO18T6HlNCUHuPSePsvFRxGaX42KDPF6EtlxUXP61LCExy1n5y0lP7K1iwJjF2m95mUmlu8Ii80wka1YvKEJhaXUN1kozIAInE5qLyZw5gjs4lE5vC0j6uXdGTGiz7ATN2HGruq7oG6QkiYRvr669eLwNjcmVzvzKnzKhGYNS2e4CzTuTL6/D250cdMiY0+hhudWeISkZnUdkv7X2qxdMmCMBepeYvQaN4StSltvfVR5GDt/9hHOGdyCSgzWbybTKpu3pMdERqRGNnGVDHp+aUfrqU/RrxCyE69Y7rLb0MOTVej70IuTVfpsa11VMYXGmmGjfH5/KTroYdfLS46X0aaZZ+wmYvM0+0kMLZ6xBOZpDT4I4n3XY2XukdkIyWfl0ZX4eh9wFlW8kgNZbEkM948MBeYlNToR+pYyeiMd8FKQv9rIive9iUf0g0A6Z/JFoqpgu+SBeJS3zupxOClMaDs+dpjWpbe+0cXGqBcaj7SOZO3QJk5G09e9LyXJ6OWlxZYSwWmrV6SZTmJyQmNLYjk1/exib/yJiG4iSRmmpcITGjlNL2gwjyb+ARETbNFZPS0HXJkDk87nMaE39s4IiMPjXf9ZgIQRObT9jRKTDPMn46hRdYLANQNUDsjXy99Tl5UJjW/RmDs8nOEJicwel4LWcn/60xogLSYlN6O1rw+MUJ5Erv9LWLBWZIbu10JqTGqPKHRx/aExp6HcE7Uxr5fvc33Fppz91vyWVBofgYoM0XIZbp1nmtZ2alpPUz147dpH3qgyHvzSA0kaQspmHkglhvv17R+XpvXeEIT/arOC41UI3Wo0AwVUxKB6VG70RhLjzqZL9Nig+d2j3bIkYlEJpUnM51c+jpup2jM7fY0NknfbE/TyOF3U1QpXIMGbkFppUWuo+2fxZ6bnS8V2iVJKREZPe9GVDLHTGEFaRadKWVp27Wycg76GFpu9DLb6ugSpKqgvJZOS1VJ3i8ei7ePpX8Eb19rk2yXqhSXtitpYQbkPxsKzbVDmUniiYv3XFcjichYmdljEh3M5cWKjJUaPa8LHz3VeOKSExpbiHmFXQeETvd8oelRYTMKjDS/Dst61GhUfzMenbKi5yhPZpKaUxuqlUKrpSacj86P8WQmFcEYpy+QKqXNth07C9w0p7FTvw1OOGED3E27HYXGdkRooxm2+bI3DEDpt/BcQUmtG/f7bf4+cuSk0TtnmWaF5ntIyaWQcy0Rm0sVkkstnWyei32tpSRqo7fNRaRyUZrvQcn7BZZlk0JzzVBmVmEjMrKsRhAWXXpYkdmFzXMio2XGSoyVGe+0LJ7I2HwHT2j025rt+wbotnjpKvRdha6r0G8rbJrQ428QmHYUGYnSFI2nBMxaM0m10ukY+pF5edqFaIxUd8h78Vri6OtjRWb7Ddi2Y15Ms23HEcM3VQsZAHODFtEAmVZoOmUnOiqjRSYXldHn6J1zLipjX5eTltR23v68+/1S5Ct1bjo6A/X8R8nLmjte0XvNiY0mJzmXjujA7K80amNfk8qTSQmN3ce5LH1IJdVGQPl7ta+l0FwjlBkXa/paXuy8jszYaiURmQcAN/mqpVQ0xgpNKUsi4wkNMC8Qk/8h81ZOVV1FUZpJZCQyI6cWDyA5zYflNhozq1Z6WnhPgicESmSkSknyYqqqR4N2HMFbz+sWV/12Ov+XrsL4/6JlKtex3LmURmZg5rPrvk3T+ubyZeqaAID3mnO2yR1nzfX3HCP7HqzYXCI641U1LTXfBtIXfilHRm+fO+9cnk7JB/3WL4ImJybA+vOk0FwjlJkZqdZKdp3Nl9EPG5G5mQpSrzrJkxlPZHShlbvJeuvsQ5I4tdDotyzTrEBNQtN3NZpti76eojRaZOIRsKf5bqySqsd1s2iMVCsdM9VKuajBTAI63G5PaLZtlBfT4DQOrWDnW2ym866m69J3FV4BoLudC0xqWsLayMyS3LhCIyLTQQYfvQip/9Gl5Z5sveX4mlxUbGlfVpBz37UR3a+OsKaKxsPrKdgKjd5WH8M72VRhn4rC2HNdSjy2N5X3YsmYzxEvCs21QZmJSDXFtndZb5mOzNyaeSxLzBfMq5S8hycg9jnUck9irAjowsSrFsg+bvHa1Xjdtui7CptxekJfTSKzGSIzWlpkqoWm76tYYrrKb63kFSheIWkL+O0LPimR2VRtJC6SIxPP97B0VQVsga4L40u9djVCFZy5bl6E5lxykRmY+VKheU/WRFgudY45oS05F+/1S0KTPXap1JSsswezrZ2gXuuJyhqpWSM09rX2eDneklezphqJQvOzQ5kpRif56mW2qkmkRm3rRVuWojJaXmw1U05kclMrNXZfuSqKLCEx+LXucOwq3G5P6LoKzfYUIjXVFJmZRWL6kHsDYMzBcZtcyzl7za4tqaqm7TdgSPSV3Jg9nkdx0TkylZEaIZKxaqpyOnYVxoRgLS9WZHLRmdR5nxOZsfuYba+iMu9N7n8od96l2Otml5XgCb1d7wlNsdislRq7M09c7Dzwdqk5R2hSx0lxqcTgtdVIFJqfGcrMyJovmI7O6HyZW8wSfnMCY5elBEY/B/JVTPp5KpriJct6N2s9XaK7Aba3QF3jBQgjb5uqp76fpEUEpuuqoTO+8Pz1uAlVHl5vvlZm7LmlqinGQj1UL9VDa6VmaPDt5cjEUtPOojOjlDWD3HT1lBAs11nLy5KUlERt1kZmvCngi0ytC8bMOa7Blpmpz8uLoHnbp86jdtblyqfUeernKaFJHT8XHY3wpGYtKaHRBzxXaux2ay5k6jh6+RJL18U7lzXVaxSanxXKTBJPWJamumpJ5cnY/Jec3OjXzOaHQsiOEyTYqhcrNbb1j36rqQKi9D9E9rENrZ1ety9Dr8FT1RMQy8vrsAxdFQRGohopgdHPc7/ivchEDWDoCG+zlV5r2jH6MlU1+UnAQFw1JvSogGYStNf623AN1LUeI0PwCz0tMrowPicyk4p0jNt7ItOHzyD3Wdv/pSX0e9TvzZNMe/52nT0H7zw9iSkph3PXNfXanLyk1r1Zamy+jBeV0ftbKzUlH6qNzthj222XOKf48czVHncpL4lC8zNCmQFwftjT9jFjqqJslZGVF3l8GR5S4LnT0B9KQFVnWCkB4gK05B6VuiHr9UtVO7Yg6m6BbR1VPQGYJMYKjBc9sgJjhSwVkfAe25ch6TfkyexwwD6M8KRaLHWz/BmpbgJCFVOnWmpJTk2HKlRbbdtQ3dRtzbGdc04VurowXPPQ18Bej3FZounw+Fn4q1dFODxxSUWdUu/BLiuVGH2snNCsEXQPfQx7jnZd58xH3GC6b3TIF5g5odHbyMmviajkCvwSs1tznLXkEpXhHL9E2EqSn73zoNB8VCgzxXjRGKh53SQb5QIjDxuZUYWw7pkWAE51P1XHQEnN1kx1YSoc1bK33oO0fOj9y/GON8A2vPBlGA5gUWK8KJIuKPT5WGGwy8br/Q2ftifs7w/YNUFiGpywxyHKi9FSo58LsvwEjOvHdbWqhpJmzq5Uqfex9CNzrdBYCZD9jvd8GRPsG2atl3Kfh8eSINjC3iMnZSlJSx3Xk0Gg7BzOwf5PelEY7/839f9c3DOyjbCs+RLnsPt5yayzlAhN6r2t+SG5VJ0Ecx65HKDUNqntctuTjwBlxkVfllRrJi01O/XcNMPOCY2Vm7FAno8RJN3r912Fuu7R1n0QhHpo7QNM3z8tFPLcio2uqll7P1z6tWmFpg65NOG1CxKTe3hokfGm99/w6f6A/f0Bm+Y0RmNEZKbITDfsTktNEBQZTjPMpwUHAD7VfWiqnRMZ+60rjVyURDFK8Hr7fetnYOeXJEK2XXp/uWPJcWT50nzpPktJCU2JzLxJaGYvVJQUtnZ9TmRKSQnFJSQm9/q1VUoUmp8VyszsS+XdmWVetrVRGnkUJP0mozP++EDSK6106NZVFU71MIhj3YWWP3JONkKSi8zYt+Z9b3MFUvKG7BynBsaBGZcKytICVM7bRmZM1Z6IzO4uSEyDFnscsFNSA8DIyyQx4TA9+tBwfJSWmdRUPep6WC/JtJ7I6M9CF7jee7PvMbVsSW5KZHXpWpfsa43U5N7LmvfgHackMmOXld4J9XclFY2R6dL/tft/7gmNrFwSgJIPcK3IrP2lA+QvZu5+W4qVFWC5SolC87NDmSnCNseWqY7SmM7xlvJlvuj5b7j98ugKjK3y2ACoqx7VXY9TG6puTkAQmuPtXF6AtNikvqe5QtYWFLn9eK8tFZilX7SpyEd0zY/YbNtRZILETBEZkRpBD4RZR9GXSXKmKM5m3M5WN73WPUI0CvnWTLlvn1fY557b+aX9C2sK2xKRsQKSkoolOSt5LzYA4EVjcpGZNUKT8ovctPTaHs1rI6HRF25t4ZkrjOGse4+IjXAJibGvzUmN3WZpvWzzlk4NyY+EMhOhL8etWeZFY6zQIC0w+vkXRJEZXQ2i+zXxcjiisYyaaXYUGtzOoyKpyEzJvc4WKrLcPrcFtvdYc4OHs62HJzP3YbiC7f0B+/vnseWSTvoNz5+jYRa8zvGmdXOB0csAjFWCL9jAzZuxkpmjVF5yUw/vOi6JY8l52uOnIiJ625S8eDKzdOyUOHnnsVZoUudRKjRWWixbZIRm6SJ4EYdU1GApuVhzyQJ8SWTWVDmtjbCsjdKsERpGZz4SlJlV3JqpiMyQM1MalYkeRzx8ecRD8+jKC4BoWcjemHI8kkKjyUVmajO1y0pYI0Slv/47sw/vOLrwi675JDL76qAk5nmMzuwHkdkN1Uya3nnjSYFxln+qe7zWHcbozJpog17nSUoqCrO0P09A9Xzuc8i91h7X+99JCUXJo+S96OPm5vW5pj6Hc65picxIHlkqMpMUmlRrJaGkszpv3ZqIzdL+U1xSYuxr1kRYKDS/Ar+4zCx9mbS02FJEJwHX0ypPYpLVTC+4H0TmAY+mYJwKUGAqNO0o0xX6SGiOXQXU9RQVyFU5LZG633mFm1d46YjMWpkpOQ+Y49TINsG20RmZt/3G6Com6RyvGiRSV/npHBogRGbqukc/VjUNn0MuIpOLBCzJTOn+lsSwRGSWojUpAclFRs6VmdR7XBuZ8SSmNIqzNirjLRN5sa0Q9X6ig+bGX1pTqNoPcanlUkqGcjeRNSJzzq8mT0j0Nm8VGg8KzUfmF5cZjb4UXo6MnZe77X5a7kVlkpGZb9h+ecT93SN2OIwyA9hk1FhsWlXVIeiO2263p+Fr1QC4cRJxEfcknJKH3K9i78dQar0WGn3MNTKTKvD1+9kC2KabYE/RmFMkNbkxozQnzNGfkRWbcI6J6Ix9T0vzS9Ole7BwTsG7Zv8eJdGn1EP/n5YcZykyU3JtS+f1eaWmunsBIL7GOlKjWxbOPlOpbtIic4kO8IS1LZ9KyInMORJjt9fCApQNwWDXeetRuO4S25NLQ5kpQuXEjPMmMiM331x0Rj3/JNUgeMYDHnGPRwCTpKRa1lRO1GAsiLfTcAGvdTf08eI0w9U3T8C//3Vqee6XcC56Y6XmXJGxy1O/7Ldtsgl2SmokMtMP17UbYi6aXlXt2Wol26JJ8mb6eugc0ObO6CjNUmQgKTGm87samDW39j5PO10STO91OXLRlNz2VmBy4pci9f9mX7/qOme2yV0jfa286+yJjNdHVHTNdcFt+5i5tRtnyG2Xi9iUco7IeBHy3LHXRFnWCE2J7KyVRfK9oMxcklzBnmCqsKhRoYP0LCsF6DSto9Y2AMZogPSDIoXpZtsOIzn3GCM04QVxobFWLlIREu/9pgRpSV50obRU4EQC+W1o0i5DEHTjcAWSADxVE3XDLubjLcUVSdW47KT2pOfl8+tQRYNmzq6RLrBS7yVapoRFhh5QHfN9GuajISEATJ3hqabwXkTBFvx63vsMvOtu0YW0PZ5HTmLOESP9HrxjL0mLPWZObvS+lyIzWlL0NdZR01n1ktp+1roJmHoDLq1qsstzCb85kbCm9l5FyJLgXFJokFnH/JlrgTLzZs6w9C4UQn0XRl4GpmoNT2hySPHbIxTifR061cO2DX3QbFsATYgO6JtniciUFkqJ9xh9570ogH2+VOhBTSXSNUbC2jDuUnMaIzE2vyXu7G6dyLRDLOeERsVn6lFuetTjqN+9CEXdT3Khz1u/Jy0vQz9DwCQrQNy7cOWMch0N1NmpcZakh+jpTU7Hzs17YqAjB8ByP0b2eBrZb8kjR07S7HLZPiuQiWXedElkrBimpraFk72uM6ERgblV09yvB+95LkfmnGRhfdFtXs8leUsezKWiN2vPkXwvKDPFnPklTYWcFXECahyhkfUSndHbTkVuNU43VQtsgaqTKqc6JKQeN0BdA93NOpmx5780n3rv3jXICYzgFUDOL/pPdY9me3KjMrZDPBuRsQnAclVPaNR8EJp+WF4elZGbvZKK8fwngfk09C0EYOxvSIiGSjDPwwCXPTr9eetBPKE+c8EKi73edhtBR5a0xGjJ0fssiZCcIzNrl6eOuzSfWiaURGa8qqMa81w2j1l1k7Ru0oXw2h9TKZF5z6bZOS4tPmvyYHJCs+a1a86BvBeUmffA3tTsPAAMkZm+iRNObZWSrW4SbMsm3UttX4Xtmu1pLOj6YQTrSGrWCk3qfaUKxtS1SUVrciQkJsy/YLNtQ74KejRDZEaqliah6WYRmSnht3ZFph2nm/G5RGK00JyOm2hE8Pn5f8MoNNu5xHgdJgq2ejFcjrC+rRr0dYWqm8vUq754napqlGtur68nHPMDx1EDPe8l7FqxscdYKzN22Zo7mN1v6vnSvH4fS5EZK4A6KpXqd0bQ247XTUdnvOeWl8S6lMicIzGpkNh7sLY33zVRltzrSo9dchzyHlBm3sSZ/6zm/99GBkRoAJGUdHWTSAww5c7IsAd9VauCbpIaQI1efVQjcK+N1CTeT3ZdTo70tqkcCi0yQ1XTp+0pjF1VtUNEph2rmjYqMmM7I7ToqiUtMjZfRiI1o9D01UxkxshIFFWph/fgj7slPT43ql1USTVjX1VGanq0x9BePxIaGZRULxJKyyNPXo5mmS3QoZbpYyxFaFLblz6363LHs8tzr9HvJzeVyIx+bQf/2JYOiVaHOjrTYV1z7Bc1L5SKjHecc6Mqby121jarzgnNmnXkI0OZ+R4kIjRdFwpPwEZmJnmx1U2aaduw07BdixOaYV2Prgp5OX1TR1UhY37H9hQGrEyNZq2bl9pH7pel955zy5buRZ7I1ADqb0PS82mMyDTDSNhxFkw3TuenOuXJAIi2ttVLMtVC0yuReXWjMrGQiMg0QzRJen4O591HvRIDeaFph3MAEMbtGjodarZtWmimN+6TuyvYVjc2GmOrU+DM6+OUCIZ3XiXz3roSkVkSHGFJaJ4wXSf9yImMfLdECq0IvZkSkSkVJNuyqnaWfy+WIjDnSsva/SKzLXlPKDOXorSKZZyv1eK50ITn8+omS7ytrG/H59W47x6oQoEHxHLTDwXfWBiL2BxVfo1umSF4BZR+vlQ95W2fKoxSEZpti2bbRlGZqaopxFK01Nh8GYtsOcV15lMtNH1foT1u0iIz8KnuR6nZbNsxGrOpWtToo/yejZGZ1DlLb9Cnoe+hCkFcRWgkWTh3XuZAsuP4uV5vRcYTG70PT2r0/nNCAWe6tCy37VIkqEh0hlZmnWotlprK9VqKwtjX2Xm5rk/6TclDojTTGGNlvFVkfjQlrYzOFZq3Rn7Ij4Ayc2lWFuTjr2onAuNFZ1LoiINEZeS1+libcb5CX1WhOqqpUQ05NVJd8nLcALWSGn3eIjT6F3hOaJZkxm7vFWRedKbGmHNSY3roqiabMyPXx0ZodDRGhEU/19VLsl5yZfquniRQyLRK0iIjiqSFRs5bzivFBhhFJly6YdtBaOq6R9/VYXiF6cTg9j2kPwMvogKzTqae0Oj/CSsvdp8pcfDm9dRbtrTNWY8hvwmA5DgJyWbxIjpPzv689+wJjFwnG6HpgKmqSQvMUjLwi1rviYy3rKR4eEsUZs3rPHm5hNCkXru2Qz0KzY+GMvO9GW9aN2MTXimGBVuQ6YLXWybI2E06SrOBLhDjPmqliqJHhaZp0TbNGK1p626SGt20W7BJjfb9ybx+pK5FjlQhsMWY+CvNsaceYE5RlU2qebYw9f4bN83WeTKShaOF5tRK0q+KZgFT4TdgWylZkYmnU4RmOj9faLrZ8g10Erjk5AhuQrC91mHHvsRIlMB2+mbzZJaiFbasPEdmctPUulwelicwiQRtQapsdUsyWR46TGzCDwEtNfYcLfY7k61qslGZGucl+6aiNEtfUPsmvH+cUkrE5j2E5tyE4NIqJFY1fS8oM0VIB1X2ub773043H10lk3m8HDd4/nOH6q4bCjDJgolHydbLBU9+BK8zOA/Zq0QfdDJpVfc4HTeo6g4nadrdJQZO9O5p8Qnll9tlW/WonWUm8TfEUOQxHwrCuw46IiatlaxatFGspImEZqyiG6MygyCozu685tbSUqk2gjXP7clH4qb3oV9XWJ20hI2q2IiKF/kA4rGFSo8j07fIzNIyPZ9NLH+Joi+pVmYVuvD/0wB9X4UfDF0F4BQJzqnu8Yo9gJv0NQN8gfEeWzXvvklvyAOodfaglyIXnckJTup8Pb6XFJRGbs5ZT94TysyI/tLZL1lt5uWftsPU9PE2lpUnTDd3+WU2u5Fu8YRhgMIm7q5NIgtabERrgFwysJ9Xk2q9I9GZCn08X21Q3fU4taEa49hV8+ER9K/GUpa2TYjL/DFEZbZTB3nz/mPSB3vGbrwuB+zwjD1aNHjGDi0aHLDDSU1FZEb1GKvklgViViCiM+Kiu+Gbf1a2Gb7M68Rl2weRJcqF8qIywpLIeNVE9vX2tblj6ddcYpmd957PvotlTeXluxh2N1TjSg5aNURl6vBcKv9OAF7rDcb7yVukRrZ5Aqaqph3C/ecZ8T3LG8dJfxiXKHg9UZFlXnKw7eTPnmuO0p6Nz+FcUSqtbmJ05ntAmcmiRWaIvkR3lhrhJvIMYBdCylZokjdQeWzxWD8AX4LUSEKoJP9KLoUIji6wvELPWx6WxV+wGv0UjRmkRpJJo8RSPSI3AGBoyi3vZymyrL/bXsFn57XMJMXmW9QcWxfpqdZKgsS79DWxIjNVVs0jMz1qnNrNGJUJBzASsVAopqIymvHXvyEnLLLeRo5KhMtlSWTgPNdVTt5+7PTSkZcliYm+j+skxvtRECXYhwXT/9t9mJwwVPHVqtDW101uKffmeS46cwTiXoBTImML0lzB+ha58aQkt8wKjT6/0uOluGR05FIiQqF5b35xmSn9VSAio28OckeWxDpV1WSrlCRKY+VmuPqvuMMjQiuXU73BZntCV1WjyADTTVP3laIjMV7Vio5UeKIziUwzykuFbohAqO0Hoem7Onwdu+10WbzCazoBf50tnOyyXESm/gZs2zEqU0/Ft3sKukdlPUxEeG9TFZsWmQP247TFBs/YYxzaYBAZyZOYtRRyhhuwBWOVOGfbfDwlNADG87F7vAiegOZ+hL/lOCUy421bIjN2XSIaE8b0WpYYHZmZLsG86nLMU6sA3AN13eMAyVm6nV8zKzH3yAuNvIdu2F/0RnUejEhDbsiDt7L0j5GLxnivXerP5qNVNXHspo/CLy4zFvuFk39KLTC24ykRmiHMK4mxNjLjiYx6vOIOx+0Gn7anof+XCpvmNBZU0ly3jwo7KzTTzTYlOVaGRGR0ZEZHbcZebusKzbYNyY3bl3n+zBpyv7Z1oeNULX3anrDZtmiGJFqJocT9ysQ3Ei0EnhzkRCZEZBoV6Zh3kAfArbrRwxLYwrFWn0tllqXQ1Uu5bWw1WHEVk8aWjWtFZqncTEmLXVYSoSmNxIz/X1NujERjGunEMCMx9jtlq3V77/OrMDatbut+uHuYKidPYpaEJsqdkchMqjpnbQF6TqREXpfrd6ZUaHLns8R75KycM3YTheZ7Q5kpQosNEP9DvsSPTuXOpCQmRXeL167GURWWm+0JuuzSvcMCmImMrW7xer21N2MtMhKZkeVhuzrkBmyBrqtC/kx9m69mKonKpAohp1pJes0Vkdk1h7EvmdBq6RS9R3l/FrtMhE5yY6zIHLCPojGzXn51Kyb9uURJpFOexVKOjMxLy7QUccurSZGK0DWlS+hd2pSL0tfnggIpibHTNZGY5CMfjZEq3ir6zqSjM97nYz+DCn0kNADwUvdAPYxmn5MYnfArEd/ZfUQGoNT3JB3C0a1xzilEc69Z6jBvTdTGck7RlPqn/mjyQKF5DygzM2ySgP5iAvObQocQldkjSgR+UpuL0Gg8sdkC2N4A91u8qCgA7uEKjReF8W7A07o4cpOqYpLIjCB7QgX021Cgv3TVFJ2xN91cYZkrbMZrACU1oVrpdnsKneOZZs02kdZDBMZGNqTgWRKZduhLZiYxgCsy0hrGRmWseuiqJj2fi9AsRWX8lla+cM3kZElwLnG3sMfJyYzMX0Rmwv/ROdEY77mNykyE76aV5rrqUd2FYx8gd5BBaKCuiZYYLTa6CbwVnLGqSUc69H0qlTdT8qHnKOkB2EaLcttqSs353PNfiq68db23DXlPKDOLeTP6hgDM/zklZ+aAKOSrIzOAfzO2bDHdwO5DlGaMwzhC06kbqq620FEKHaGxN+ZcFZPsS/Y/Vnc1J7THzfDr8jb/fjxsIeMlRTvVSkv9s6QSaUcRG99DHS2XQSVtqyUtMsenfdhZVK2k3nSm6iYVlbGfif78otdjnjfjD9KQkBxbFVZS9fNW1tzDPZnR828RGcmxUkm+8r+kc2N0X0TLUpNOsre4gj38RmilywPsw+cioqLvAVpstMjoCM14MURkVOvKxV8W70lKaID1CcDXIgRrIi6MzlwayozLmsQ0uYFIhGb4ldSpTuZ0dEZHaewh7tXhAQA3eMUeJ4Rf+dVd6Nl36BZtdjbxr/6yG7NXxRT2Nc33qHDCCT1qdKiG0bhVdGbNvcYTGNufTI15fkwTd/eve861hYYX2fBHagrPcyJzOjZhMM5oZ8t5J1XdTVVNSjlSUZkwnReUa5J6vXyZsJM6PudzyoZz7hQiNTm5KZEZmZbKzPj/NFVPpgb1lM8j/l/yvyteAnApkXo208U4dhVwv51XM2mJ8TopjDqtvFFPbItLIbX8reSqlUpyaOC8Bsj/wPxRrB3moHQbcgkoM29Ch2qfMf36GKqdRGa86Iyeeow35xu8SuHUh+EHNmZTuUnq/mrTN+nppqybYMciM433A8gwCO20hyYUmGHIA6d1Ru49ueKil5VFY6b3dxrfj8zLewjnbkfCnno9luezjvGUyITOAm/8++7s/X0DZHiFRFNsfb5Lv/RzAiaP4s78dDWGnebe29L/aAlLOZ4podHL7DZL26mIDBD39QPMc8xyImMpicrIMSpIC7o+mm6aoTpKfhRsb2OB8ea1yGih6YBYVnR/WPKwPdnqgvmSUYK1QuO9RtYLbxGbtzRDP0dWGJ35UVBmAPhVTaU2beufn9Wy2/gX1DEx3Zr5THRYWutsho7ipDXPHgds1DT3y3J6Po0BFcvLvKpGRogeMz+2Fbb3h8HXtvE563PXz201Uo0ouVdGkvYGYNR97fjvL18dMK+qscXYQhJtVJgOvfzqZtimGiM39tLSr32pFoulq1Hzk5BpCXtu97GEHW8noU7JjJ237zclOqn7vLd8zY9Tr1zT55DbT/SeJFoRmkT3JmfohAZ9NY1QHqpc5X9IsmLiHDOvJ26L/ixXN53PVbOlIlBHOBvaKiYtOeMFQiwT3vx7FLQ5oRFKouIepXLwlkhJSW7MmggOheZSUGbOQv9j6n5mJDqjx0q5SUtMSmqcvlvqMVFxukVOxVg7iswOh1nOTKrQ11VMGi9HYzN02Cdn0FWVSQYeXiNRgNQv/1FmXhYFRlcp2fl5QaMFRkeepBjqRkmYPsXCvJPxQ1ASowaS1EMW6CqxWMDa2ecSLokfAdC9+04jdctgl6oDPzVOlCsyR/O/ZO+pJT88S9H37LdIjd2HFqvUMey+O2Bq6YMQRVQtioS2SktMhQonzFs1YfjeeOgfAifp1gBT78yL/2cl8mKrmcaqpqXojO53Bs68vZDn9u+Si87Y9V61kvePU8JbOtJb8/7OFZqS8yDnQpkZKU0EBuJfLjXiG4fKm5GegaWAtzcib5rphC4IzNS3ip3f4YA9nrP1/2E/UxFuRQbIRWZ0m5ypuknGpYlGEbYjCMv9K5ELkxOYueBIR4J+nowuaGTsonkvwH40RlfTuCz0GLsUUZJIgD1PTVwtNomMrmwbozJ9g9Nxg/bYhAL72Mx7opbrX4qWiKUqIvua0uWa1DFS8uItS0paLDR9XaEfBHpEuUWP+VhoVnSmt7Y87EQ8AruSmt52tujMW4nJiU4HsyAXnUlJDJAu1NdGa9YIjd1vaZR86R+z9J/+nMhISUSltIUTozOXgDITkep+G/DNWt9J9eMZMfu5uGh5OWZ21U2//utZVCYUjiIxUt20w2E4Oz9iIcs8kdG5M0KPatxneK7+be6Gq2JGDg7zcS+5Ug0jEhOqxOajRusWWVpgdJQjRzwI55S3cJptZ+QMcUd4Ue++zvg9zbZ1W8VoGbN5TJvZWfjnrs/NG1t7MRpjZWaJVDWSjYqkXut9LVLbnFPtVCI0GvluAdBCIyOHt8fGiGgVRhpH6C1aBAaIO6rUPwokaiN4nTVacZblLnJf0PNLj6iqaSk6A/hRGSCdeLtU8OZ+AJYIDTD/x1qz/7fwVoEoqU6iqHwvKDOr0F9Gmzzn3WlUayfbmZ7chKzceL8yB+LbYhylEakRsQnb56ti5vv2RWaDFidssBmag+9hBjrcTknJWgb0fNdVUSRGqsOkeNbPp0TZWBD0cjk3fZ7TfFjfDn3m5CWmnt5LP414nMKKzKZJyddczuQ9jNfEVDfEPRXH0RktMod+vxyN8aqYNKXRFqA8OiOvOVdavGPr468tu6Lt50IDBPm2UlNV09ho4VRisQHmPWqHZfG4aTa/yVYxJcfMWiMxxdEZqQL3LpC92PZk7PI1BXQqApOqVsqJjeacpOBzpGLpH+9SLZwoPW+FMjMjlWUv87ZHTX3jsBEZjZEZL19GqqIcoQmjLfdjlEJGit4MOTNh3Gd5hCiKrVYKZz0Jjf7d2GIepQnvuhqEZuoMzP7ihB5cT9+fm7iAliiFjsiIxEjiciwtcZ85Onl2Opd0HzISlfGqc+y24/MxwuQUNKpqSUQmF42J56eqpun85Df/vFdiue4ynIQVmcPT3o/GdEhHZvSv/tSPYo/cNil5OVdqBHt+ttqrRHBm6wah6Wq81h1ehyjbuHlXxS2eahkR2xsuJKzT0Rn9HbNVS9HzvjpfZHJVTmOekBedSfW0C/i5Hd5yKzSleDZcGglK/fN9pIKfHep9BCgzLjYcascRsa0COoRO8/bquRWbHWZjN9mpFhkjNFMvslNMYYrKyAhC7ayaCbBDGUw33NZEYTxsTomultGFsK9CMbHMhKlthaVFIJaZuPl1rslyLDZ1dM00UQKwKmDcfJm6G6Iy8yTfXDTGkxubPyHnMl3vOJ9nqqhqYpF5KhAZ3deRlhmZ2nKmVG5y9+Hc+rX3cO/3xLnRHgDjiO8SpekqvNY9+rpCVfdjpAbA1IOzkpsTwncR8MY7i6M3Nul3cbgJu3pJYpLNtG105lk9F7lJ4fUUrE/o3AK4pEpJjum9TvO9i62S932O0Cztg6yBMpPEExjvub456PkdYqHZhXXHm0le7M3IkRgg7kW2HgrEqRXTVL20G8VgqmaS14Xn5b2XCrrQ1VUzNqG2ZL8btGPkSCIyUi2mpcYmLduE5lDtpZsqTwmxQV4qtIjD/vF7it+HjsrkqpiEECWbVMhKmI3G2MiNxGSsTNpqpnGQz+E8Q45MM4nME5ZFxpYhVmq00DitfaLX5kjd79cu1+RyY0peH+XNwIjRDUIfSaGFmkjNp7qfpFa1UgMmuQnLejdyE06tn0XdJCIDpKJ+anrOQ3fpMIvO2GTgVIGpP2i5uJcuYHNVSnCOtaZH4PcszrRRp3jrkAjeNqQUykwROaGR9cD8cmqhkV9H+7jgsdGZlNAMC3TuSxNFZk6jFNhqJj2/1CRY06NGM3SWF57PIwq9IwxeKw8AUTRmp85Vz6f6yLFdzp+G6hpvSIYWDRqcxqiM7ftDn/ti2F9FacbO8CrpAK8do2IqdrKY9yPNqxu0o9DYKI2Nypz6Jgwj8bSbREZkxhMZ+z8GzAtAYB75iD/I+TZL5ISj9Id9Z+bt+a0JENhoxmyfSmqG6ifhdZCXvlZVSo7g6MiNzrcZJVm1XHKrMeses0J7SVxyrZtm0Rnvg/dIFbS6gH1LdMZSUqVUIjep/a1hzT947riXjGaRNVBmzkbfEXWnebovh6WQrlrt/WKuAWy/odlO1RdSaM6n7RD5OGSbLtvWFxWqqLlw/h37Bb4VovnzcB5TaytfZCRKUxqZkaTkEzbYDYKgl9lz13rUosEGbcgVygRjXrct0FX4pAa6jFuQteN7SPWJY6M2YU2QMTlXOTOJ2wBTBOmEJghU3YeCrzY3dEd+Z1Kc+je0ZZwuML2p7M+b2vmcyHhVRkvzOenKldup53qftVQ/3arlYf5VXbvXIZLzItKj8m4knwoIktNFyfBKonWLuaW8J3stvetqr8MsOmMjM6WsTfR9C6VF0aV6BrYsfVEuBaMz7wVlZjVL/dEUYm9CMrWFx5BsKgVhMxSF+6GaZnr+POvLxIvMhPnyG4+uYrLLptNfkpnwXBf8IjIPeIye6yoaHd2wOSjzpspBC1olM7EU1GN1zV5JTZCKPnQCWNUhd6Kr0Hf9ML5RaK69GURmXx2iqjFbTZbqW0YLju61V85VphJpCucdznczVDed6g0+1X1ojZP6geg94g/Lx/7yl2n0/zh0Gtg5Iz17IpM7D9nWu5fntk+xJDEzeXGm9tp431Eo6akB1Lfj5/EK4EWa8M/O3bnwuv8p/XjCXG5yHpIMuMiKHaYfVkN1d3Txa8x7MvciIl4Ba7uu+J6sidqUsiYMKdh/YkZnfgSUmSxL4tJl1usbQ4JkNGaafjLJpjY6sxsiAvEj36lcWHfegHn29bb6Knc8W81kRUbmK8xbM2khkPWnSAaaaIlUOwlxpVWtruGUZ5OTmmbooVhykqzI6EiNbZVlRn4aBracn6vOw5FzPg3bnNCMfdqE6MDyYJcA/KiG92veq8qIZCZ0dhh1jAggGsRSC40thG1/N1ZkclGd1FfIvreSiEzJFAXPU/uth4hISja9ZV6uk75+XsOAlKgmozPDyO/ZFpd2ZzrC7LF0//ve5I751tZXl4SRl/eAMlNM6h/cVjGdEbnJ/JrcbFvEORlTdGaa2kfryopd1iDfgZugmzGnKKlq0tVMnsg84NGNbsyqbfoWbRX3gdxig2fsI6mRJrL2oZVwjMwMkuNJDY6bQSglIhZLzQMeo96Xne7tZhGaMDr3LjrXDRo8j9VN9ZjcHZRrg021QVXvhiqORIEJpIUgVTinhHqLMHbWtsXt9jRWoUiVSd9VeO2GzzklNyIxunO3pYiMjfRgYd57D968nnrLvOtZcs1yx13CViktVTWt/oGvP9Rc8+xLIftPdT76o3iPKI5mKTqzFgrPWigzZ3FuVZOqj196DAVJXU+/7kPrn2c3QmOnqaokLRknNEMEpSxnxr5+ysOZD5boyZSNwFixsdGNqKqmb1F1PTbHV9y2wEvzgtP2Ew7NPorNHLAfn0myrTxEA3d4HnJmwvWKJAbSumkjb2TMk5G8GP0eRGQe8JTs0dhGZk7Y4IA9NmjxjD0q7GEblut8mQYnPEtFmbSoyYmMnl9KBdAFsI3G1BhFRvKFZOgK3fJrJjddBeB2HqnxpCFXth6dbbxIjn0fJZGXnNRYUpJTKjK5u6yNvqQiMktSY+8f43rbsglqg6Wp/nFWUrDaHEFbuNttS1mqXzyHVD8374Vn7+SSUGYuih2rSW4Ihf/EM6npsNmGQn0emZnnz+jpErFsbNCjL47UhFNNt5TytgEQRWA8kQnzzzOJ2Rxf0LTAzRHBu1rg9g64bV+xaZ7wfH+LTRUiMtKiSaraQrPtkC9zGve6wR4HpYNzNkAUpRF5FNmSc9YiEyJLc4mx+TQH7MapSMwBu0FfQnWANM2eJHXIHZIkYO8mnMphqdWyVMHuyvQLPpmBQFENzYzr0OuzJzenYxNaAh0bRAOtzvpDwfwen4rOpKqjNCXRl9J5Sy5CkxOp3GuBeVWc99x2pLlUBTcTGv3BlrI2MmCrnHJiA7Xt0jGW7p1vFZ3UD9NzqprOjc5QdC4BZWYV+h/c9hS8ZPjOP/VCdObT9jSM+RP/0p8/bKumMimx8iJNmnP4wyCk+6/Ry2xERrdiEpEZC/6ExOCopluRmhcc715w2t5iU02yAOgWQbbdVzMKQpOqalLPdd8+u/H8px6XtaClJGaK3LQ4DGnIupXWc5TjU6NFg2fsx2OLzHyq+6iFTURKDHLRBU9k6m/jgKCS+Dz1XtxPvT47clPXfRhqAQAwCA2Q6H7fOfec0KSelwpK6bocpSJTIlL2vZY8cudlgysApugMsNhL+fhCO1yLndd44mJlpVbr7DFzOTlvQb/+IxR1+kNhNdKl+Qif8AfnQq2XLLlfxkOYv6r7sQCNf+3PozNR/kxfVm3UVnN5STW/tngJq+GtpKubpmhGnGsiIvOAR+z7gy8xT4iFZoswyOUxTLct0Ny9YNO8oLoL/dAAmEnMAXt1vdqxmik0iZ5LjDzfjNc/7s9H3ofOm/F6Oo57B9YZPLZP5akF1jN22OEwik+Ddux9Vl1w+eBiUpGNFLP/wXYaFLSaqsnC7iSJGjO5GfOMMFQ71R1CPy7OMVJCU1KY5wr3EkkpXZaiRGRKI0VL72/pWliJ0ccYr483YvVSC6YcqW4nbMMHT2p+RBQiJzbvdI9/MxSeNVBm3oT3hdZVS/LPuIs3yf2yU0LTbEMhMnXQNiWebhL5MzsEGShiOxca3Wtu7Ra68eABeqyn8Pp0VZNu/WMjMiIyu6cX3P6JWFz+dOaVyMjzmyOwvQOq7ojT9gQ0QWYkX+YZe+yHhNqQs3KaRWVSUiPiI2ISn/88CXjqg2bq6bhpT9gcX9Hct2iqeJgGS4sNdnge5Cse4qGS5tkpvGomIF3we1GZoYNASXyW9yLXA0OURvdwK3LTVhtgC3Rd6P7/FQC62/j/3Ttn7z3YR0lC7NJdLbV+rczY6Vq50Z9LqkptbWSmM1PZT3TAEnSUphQvQu1FIuyJnVNolwwNkMILVZ4jNF5V2blVTeStUGZWY78IWlrOvJwLL9PVODY/xU7XUHU9qqobTiEU39P8dJwK1bhMppPYVMkC2Z6/fS91dIweVdeHdJAeU8HlSc2fw87kLffx89seqPtX9PUBh2o3iMwBB+xGkdkPz1MJwLbTPREfLZNaKm0zbT1UQ9O3kaTdti/Y3P2B6i7uEFD3r9Or85DzfcYOe+zxfH/AH/e70NmavG9dnbQ1z03Eb0zyvUec9DsknX+6P2B/f8D93WMkn7bqLhxaEqb1AJ896qoH7oG67nGAjFStzlemXpRmjciURp2WWPvVLZWVkmWdM7XLUsnAehvv/KJ1MnK4Zz2pKE2q6smrMllTcOv7pZag0v5ZKAkkhjJzcVKX9O1hTF2I6NiIPNfTUuq+R1+F10iTYBGVXkmMlpfZPgabSFVRyXLdSmfqhfc0aMIJVdNhczzitsF0j7U1ZvqGXqn15tA3AHbVC/afn8dm0PuhaN4PVTeNE5mRcZ0kylCps5bKqpCQPQ3sqROxZ33P/HnEVkvYUF22bYGqexqjNDrB+3GQBjtytnwCp2aD9ssGx+4/MPZcqx9P5pppcclN719we/+Mhy+Ps84Bpdk84MuMHvJCmslXVY/D3R5V3ePwtB+Knlu/8E5Vy+jP3D6X/4MllqIZS9vkXpeblmyjj52bLkWmlqJU47obs8JubKUGat5bBrO97Z7CCsu5VVnem7tk1dA5+/Leiz3PtwgXZW0NlJmLYb8MdWK5Wb3wCeiO3uLndpRlVelTf4r2UXXzSomqA3p17Goo0K3EeH3L2GiNrm6Yn3+t5quhpVGrRGaD0KPv0IXc9hPq/hU3UhjIbu0N3BMdiegM87cVsL8/oK02g8gcRt2QSIdkg+jBH7XgnBDGThJhCQN96nZRp2igTy0yD38cQzTGPoZI0203RWk2dy322ONR5dWIKMgAmiKAJzRoPzfouxov9Q7ZnBQvEnMPR2aO2N4f8PD5yTQ7T8tMSmgOUVPzHodmNyzHJDTymerPNhe18P4HlqITS6ytRbGvs2W7N82tS8mMt+wSkalZlEZHWLSMaKmBsw2ceSSe26iNlR6NF615j1yb0mJP3vOluNT5Ew/KzJt5v0toR3DulMDI1H3U0+uqrh/lxpMaD4lF6GokmGqXHFZgBGlqLH2neGIzRmf0W6+inZfdwLvwut3TCw6fT2O1kpYaqTqqB4mZV51sgOF85erq/KQ4AfsURWdGkfk3QqTkTwB/DFNdffZ5SF5uj2juQm/Pu6FpfZyVVM/arfVfKhzqDsd6D9Tb9L+ilpmZ0EzVSru751kOkG5lNp1X3CuOXWYTmwFgcEI8dhVepWO9lMR47yMXrUj9L6SW537sl3ydbZmt571lS+tzEiPzqUhM7jro/Sffs5UaIJYXIB+t8d6Ifp6K2uSiDinp0RfvXAt9T9ZEZRh1uTSUme/CGZe507NxJEa3f5FletpVYVr3/dAtv4z++ykSmqp7DeujaMxUJaSjLVKsSi5NCttbsBaE0AdMEBktNjVC3zDSn85p+wm37et8AMheTaVFk93GRG9u/wzRmUO1GxNZp/yXqTWTnGN4r1NVkwjN1KB7EhgdmdH9zuz/VCIjAvOHmtd5QEMS881n4O74is1dqHpCNVXZyHRqoD+8i6pB9Tkk6oaapW24BlYIkiITqpX29wfsmsOUiB210Ir7AvIihDZvRuf+RB9fU2N/f8BTVwHddl59kvqaLBXgngx4ry+RmNIyMlWu633Z+dS63Pnb956az0WorJu424nUfDMvWIrWAHPxsK/z1gFl1U6pKM6aD2zp/nupMZ009nxz58mIzSWgzHwUEp+EjmzIwINAKHhTj5D70aGrqpnQpJiiMf1YMOmkX92Ae0lqrHjJuc+qlAaRmVU71S2+1aqqSaNv3MfxgNO0wSQ7AHAXojP7z89DRMYk02I/vpdKnSuASGjq4TrEfftIzsyUGLz/84jtH/BFRh46MqMfd0O/OX++AH/7ilPVRFEZPUJX6Ol4GJTyLrzVQ93jtd5jNm6TFphRaEK10v7+ecyP0S3L3GbzOMzEZd6+bRr12/5P9KiAuxBxPHYV0N36Hep5eJGKpUTYpefA+WViSl7WLEsdOyc0JVPZt1dL4tUYRceT/53SaI0XsZGd2oTikqoiK0NQ8574LH2Aby3ivDBcaptLwsjNWigz35U6mizd3MZu4lVBMT2f1ullejsAQDWP0KTQeTNhn3FkxhZSXqEleL/YpYn0NKp151Y7dVWFrnqZ/17yfonrHmV14nA1TP+MozMiMo94GJsbS16MiNckMdO87oVXj1QuYrPHAfv2EJJ9JdFXROafw1QEx0Zm5CFJwnfAb3hB97//ofJkpsb5z9iPUjPqxF1osv0I4BVGaKLcmLhaKW4m/xRFZXRkRmTG+1yt0NjhNHQksUeF03aD0/YUqpu2qofg1PfC+/y10Mg6LMxblspWT6RLtlu73iMXYcrN2+N6ZbEVmqzY3DovgppP9SNjozL2Yq+Jytj5EqkpveilUZkle9UsRWUoKu8BZeZdKby8BZvZ3AQ9H/cjO60foyyD0ABeVVM/VkuFU4mrmmwLJps/o5/bXIr4vKZ2QRKhkSEGpNpJclJ6PKOvgVtdwOnm17r5dq3m5bmRGfwZojPN5ynrZI/DIDR+HztWaCaJmYYXkOej1Bxf40RfHY35NyapsZGZQWDGaRvO/T+2R5z+8jWKxoQuB7+O1U2RUDYAvmAuNKa1kq5WEqF7wFMkLQ94xL2SGhGd1Gcb99MjHf9N/9gSVexQoWsqtNsNjscNsL2NozI2+lH60K9BYpkmJTJr0zJKgg2XYOkYqTK8RLSso8jUFZuUoNjqpFRnfN4y781oidGvA8qkpoRzq5dyH8ZaUWEV06WgzLwLl2syaPNlSuZtrouQis7o5tn6NZJHc8LUgsnuc4moSfEgMRKhEUEQkZHozAmbkLRcmYRlERlbHWGlBpgKxkEsbv8Edp8PYwLwIx7GpOCweUgC1gSJCddgGvH6FEnNTjrRaw9xyyUrMjL/L8z7zrlDLDZDPzo3NfCl+QPtXYjGfMFXnIbIjAyoOX1mwxvXQtMN9U9buCJjB/tMPe5HyXl25SUtM/r/sx5ENnzKzfYUojNHpyVWCZ7MlFS/AL6AlFQ3eedWWha9R5mVOp9czo5d7gVbZN6twvOiNd68Fg4vBOSduO2lWJ+oV011bl8z37O333OiMozcnANl5iPg/RrqQnfwcTXSvOXSSfVB0mIzdHzvNJNW0RkPSQKOl/VR4VSCLtjk3HQT580gMfWwRj+XbvtbbEKLrPp1HmnRp2jvE/pmLIOBq4Tbpj1h08QjWEtkJkQ55h0ESvtvERl5d1K9JE21N8fXWFCs1PwTQWT+wFQ9onsylscWY2QGTejR+D+2X/Fc7cfqpoORGVsFWDf9JDT1Hti2s/yYqC+cjMzc4xH/ga+4X5AZ3Xxc/y/o/1GdbbRpwrhPx+0QnbHVTEtVTd7zUpmxBX5q2RI/QmQ8iffW2ef62toaEy+qY2Wxxvzau0nD5w6PkBKTpcTfXJTG7v+9WZP0Sy4NZebN2F8UQHrU7OG5Hg3XezyFx+tT6O318U6iCPsxb0M/ZFkYiXk/Lu/RxWMvDeWMbaLdVZM0AfEv6SnfQeSkjpoHh/yNUMiKtOjCTS/fqGXTiFPN2N+L7EfOtfvPr/iP+oib3HBRqZt5jSAKn4fpHdA2m7EIn/JQNrDVdXrgBnk/thpP5ouQiFKHSVTs+U4XP6BkaPfbCx4+Pw7ascMDHvEFX0fRnM42fN6PCE28N3874fn+MA4U6UlMqdDIeisuU0Vk3CGi5D9FI36Pka0gk6ftBse6R9RPjvRSLPNy/ewyje44Lyc0S1UwS1EMiydJ3nMrEEtVYDlKpctWHwHz/7uS9BIrMUmpAdLVUBKp8aI054jPG3pbP+t1JR9Sicicsw0phTJzMTyBkS/wDlM98q0vMk+YyQyebnB42mN394xHPLgiEwqIoALSPf9maH2jq4VEamr0Ud6M7pMGQFRFZQtvqSqSAl4qXA7YRZIyzTdjwRY6o9Odv9kG0mHIAZGZ8dh/+Yrf8RRul94v9tx/8G8YReZ4B5U427jnOkW55rlIurpPX5/xXGujtCIvOtdHHgu9FwMIEZzPCFVk/wYe7h9xqIJ6POBxjMzo/4FRZFS68OPdgzvUgh25XAuLlzcjrw+Dbrbj/4AIXw3dyV8TSYuMaxV6T55ku6p73G5PeKm3c4mpkRcY+z+Qy5WxkQhv3nueWmb3nzpWatlb82y813q5LyX5Mmuw/uFGvlJJw57U5Ebw/kgsfWCXqjqiyLwFysxF0QKjw6YiMt20mS3ctMTcT88lOnO424/5GdOv8HacSqdtz0Pvq1KVEiIi7VjwdlU1dKQXn/m8tZTunl4K+nos1G0qrBYCKzHeVFoR6b5TZOykmTj8BfhSPYWEYO+Sp7jHKDOHu+3YnFkfU5+vvK84IpXKD5mayCfPyX7OOmk5N7C5NDH/Yzr/h7sXHP7yGCUATy2HphZWk9i0OIxjSE2jd1uJ2Q1JwFNT7KlFkzTNHmWmP6Crqihpu0cdicwJG+wHKZUqsUlsYrmpqj2qusPL9iVEZ3JCkxMbr+rJmxdy0bzUdnpZabXWLIKR2O4tlFQZeayJ7uj53LLZ+/F6GbYC40VpvLBSDi86/hZSicXn1iuWDHtA3gplpgg7oqr9oumwp8zrQdnkm/4C4BvQ3cRRmRpuNRO2GKMzm+0JmyrkaOgu9SXqEvd/MiWySquhbqx6qtHX/dBhXugZ2OuxVycS26iMlhgpEufd0dnpBs/YYYdndZahQJTqE+l7Jm56PkjF5wp/xR/hU6gwRTzk49BTDOtVVEbO66CqxVrMxUtHjazI6Co3L1Ljfpu0xEg1Uwq9XYMo9+bmD+Dh7hGHZjdGmPohSiJCOzUTlwboh0hkPKGREb7tyN+6o7wHPGHfHrD/8xVt84KmPqFtpK+gqfNDaW7foRolRg/oqSOLklT9XO+Aup/yZrxqJs3RWZ6Liuj5XJVLbt57biMTuXn5wbK6/Ppmnt+4W43HsueZe9+WkqomT2i6zLLovO2JSZTmkDmwULrdudgPJpWH4wnNOc2wWb30HlBmLob+daDntdx0CL9M9nFERsuMRGbU9PXrHQ7bFpvPD5G4SMGwGwoM3Tuth8QW0u/A9igc54fEQtOMVR0iMroaR6ZadKTF0B6HodqpHaVGOrOTqh57TgCAz8CX+o+pLLPVOHY6RGZCVGY3nq9EgE5KZOJBHedN3fU10IxiU3+CjA3tnpvkEncIMuYNkKgLvy2mBOJByu7+eMXDfz6N11+aZo9JyKPMiMjsh+iMHghzLjEiHVpmolyaP5/GwTK3R+Db9hVVdwzVlU03VjNV6CCtlbTETOLdzaRmsz3hU93jVYuMF5XRAmOF5pyozBqZSUlqKgIny/VnvIWf25Pkm7ORnEhCalKbp1gTiVqaLsmOO2wCAOxRVpC/RwLv0jAJJcMuaBiR+ZFQZi6KjdBAPZfIzPDPLDdkLTNaZLZqugWOT3s8b094bHTeTDf+Eg9RjkkyhArSEd400pJG+pjRAhPONq5SkRyZDlPX+hLNEEnQCcHPKsn2MEiOVH+FX/OnQWaaUX2kCNZSpatzeoQeZL/85x+ouvCD/kYXHnbcpi3w5+dPeB6KZ4kCSYWHFpkp0XdenaQf03nFLXeS6HOykZmj2a5R228xiYxMG+Dh7gmnu/A+JDIT+op5HpPEQ1QkXH3JkdFRmeWqp8NYrbR7epmanLfhHG5aYNtMUlNvQ19FU+J5iMzoijxd1TRJzQZVtQ+tmuo9ohHAbYSmVGByz1MikxMc+1wX3qkHzPSoXivLkrUW39RGL+YFEiFekJoUpe9Ppp4c5YQGZirroqiURGl2mFc7edVQwqUlwFZNefdr4Pzm36ljejAqcwkoM2djq5r0ciD+UsiNST+crtxFaKSTMyUz+LrFY/2Azd/mgxxKboT0RCvhfgBjkSyjW/eo0FUVNuoLpAtpmeom4VZo4p5WdPf68XSc7/c4HYeBAYbqMinkdMErhet0JROycDd16Ff3PaouVJtZwfm2BQ7NfjyX5zGSFI+0ZPNlrOBIZ29yPbTEZIVGPnorNNOLp+0aM7WRmT/Cuu0WeNg+oq1koIV2rK6TT2SSmmdIkrBO/rVJwNJx3n6I4uzxPFYr3YjISJPzZji3DrjpgW0HVN0L+voF9bZHVcn4W+14HtNoUu1YLTpVlw5VTdsWsInAVmS00CwJTGqZ7BcF09y8HkrBe9gfK/L6bJlsRUYLjYcjNZ2z2nuJnl+KQunz1gIjx1uK2Az/L5O822onL48md+LnYjvv08dL9WRcylJUhhGZ94Yy82ZSzQTtXUXf6YzMPA2b3WMuNerxut3jcdui+txF1U2hcIqTPgWd8REEJazr609RSyabHyPzIja60JcCf8qZ0QKzG6MzB+zw3O5xeNrjZZCZ2+0JzXaD57rHptEVUqGgex46sfOEQefSjNUUVYu66lE14XnTt6PcnLa3UMM/jvkyOvnXVi1NVU46T2Y+VIS9TrOP3kYOBqH51qmix/6KlZt+jSAN0ivwIDLyf/Bb84LuP7+iRz2+G6mu2+GAZ+zHSIuOfHlRGZEXLTGb42uIxuhO/ERmpB+cBtN4Ul3wEOAF2AL76jBe86kl0xSl0d0JRFVNtprJE5mjuk7AvMxJlRlyXXUBnprHwjzUuViB0Z+9li/5rssjWbZZkdH/JDa0o0/OidLozVLRmCWR836bpSJMqeiMjVh1QNzqSb9I2DlvZi1WKLyqKtsZnxeJWYrOvEVkGJW5FJSZYmwSMODHi3N1u7pV0zcAN9NNUKRm6zzGm+ANjvUDnrcnHJrnMb4g1UySXCtCEIYG8JJYldSofwHbislWs+h40FRE2a72VSTkzx0OT3u8Pu2Bp1CF8FJv8bJ9waftCVXdo9m2qJTYtJh6tZ2f3yQNU4+87Vh9VaHDqdqgqnpsmpCU+mwiMs+D3EyNhafm2fr6WKkJn/a8NdM4rStEOTPy76EfLfDcArc9UHfAjf7lrls66ahEg0hk5PlDc0T/+etYZRdaET3jgJ2aHkbJsdVOM7ERidEdDT5hEpojppwfG3G6i6M07WevemmKyujPrsIeVRWaab/W3+KqJis0ukDUy9ZEZWRq53Myk6pm0vO6ulgfW0tMFsmRsSJT0hmcnMwgNKmgsX4PdpqTHi/ak4vGeNEZLaPy+vH8BSswSyEjwP8xqcUkh251qt+Y7RMn9doSKDLfC8rMxfH+QeXL8TzMS733Pq5PT0Vlav38NrRuaqY0UN0EV3esd8AuUpnQsmmQk7qK8mVSTbMFmzsi0YoO81Y+LTY4tRu0xwavxw1wvJne5xZAd4vX4y1ety94OW7wqe6x2YbqhsN2Pw6tEEeCpiohqTrRImOlRqIVX/EfY28pX/Fl7ApO5p/wEEmL1xRbjqn7n5HrpKclKTQRufuclQb53xjk4vZPYL89oGrC2cpI5CEJN1yPqYVRr3JnbC5Ni6Y9hSol2b/0ZNybc7BIr8yKvvar3lJRrLE6tO7wUndxqyYrMrqA1JGR0mu6JjKTi9DYSIytUrJRIO/cZr+DbNXLByrovDLdE5elfegpUCg0ugrKi57UZp3dn1Daz4u8ISs0qefnHGfNdqQUysy7ob9c8itLfmkdEC79IDY2MqNv5vX88bJ9wPM2dIimq5kOQ/XCBlMX/M+qCJ76JDmNvf7G0Zop+gDMR+vWWKmZPe8q9F0FdHVcX65v9MdboL7Fa40hAbTDp+0J+B2hB2BM/dlMgy3uVAeCHXTfKp7MiLDYfm0P2OMrvuAwVmvFUSmZSiHsJUTL8hmZEP5tDdRVGHcJNZYFyDbrVsKx/3NKwK2q6dOcUqyn6qd5Y/kTmr7F5viCpsUkMl7VgK1ukP9PE0F6aYDT9nasutNVefp/TcsvoMaV0r0B6/9/e062Wq6UcyMz9rVWZOShc9+8ferIko00AZiExkaB7XOdBGxPdP60iJyQnFPDk5IdV2aAudDYDfU/oY3GiFxoloZBsNgoTU5YyEeEMrMKr6pJsF8SezPSX5IOU5Rm2FZExk6tzGwBfL3B0/YBm+0Ju+qAZhj9WSp5mqFgD5GZSWJOQ5Ko/IqfR1tiqdF4rXrid6/W9xW6rsJrV01RGf3LGpjf7OtQh/66vcVXAP2XCn1TjQWiVBl9wVc8j9GnSWa8aiep/poEJojQs1kWv8/aPPdHKLf9zIQol7rx5X7pW7TY2Hu0rdJR4z/d1FPVTrXt0dQtNlVoGr3DdO3kumiJqbp+qlZq1b5TkRj9Php1rsN8EJlP6KpYZERIreza613X/bR/HeXQXxlk5kvwxGVtVEYf9+g87PcV6jUwr3erXbxcEt2jrpUa/TrDW6VmKXKoj5MSX7s/L8o14t1bdR8ztkpIdlrSKknvb0lQbBVTTm7OMT0K0ntAmVmNJzRep3oWLTFAuPR6/iYWGfvrVx5fZZstDk+7se8Z3TRXJ1eehkJdBnWULBdbpRLO0I9MeNEHT25Goekq9F0NHJv4pq/vDUjM12G05z+OG7RfNjh83o+RGcnFecDjKC3zqMwkNtJEWEuMzu15xMMsRwfR6Uwl+jT8ga2GMlVyuW/U2mooQaqZWjMdWj7ddsBt+4qX5hWb+gWn7e3QU2/4nOVTkkhM1SFIjBYYKzKe1Nj/xSZMv21D9VLbTBkyXnWd1+Re80nnzUhkBnAiGIll+jw9PJGx78vbh1cAeyKjH0+Jc7DRJbfMtaW/ZwHefIJzpeacdXK8lNx45zUTGn1vBKa+aGxui2wP5JtW6/X2pLQMedVYb4nOeBeKIvNeUGbejL3RaLHR/7hStSQP2xzxIUQxnsxm3mO40R+3UzKwiIxUM1WqoJfxcHo16rGX4DtvwRO3dtJVBBadQzNVMd3E4fiSHzHjL/FbHI9/wenY4PRlg+cmyMcD7vGAh0RUJo7WiMxInzfzjvxD6yZgquoQgQkiGKbh/cUCU4QuLIdLFlUxLb3OSoWJzESRizoIym0DNO0LuuoFfX3EaXs7RmHGZutaXPS8PqZ3PhjeR4VJtBugq0L10tQAOx4uQpLHc9WWVS1JwB2iqiYh9b9jIwQpctEYLzozm1e98doevEVeclEZ73FEfNxZdZN9Q7p66R2jMueIjH1dKhpjX+Pu9wahqsmekL6v2mhMKlpzTlNr7zWpefJRoMycRSo6A4RL6s3rqEyK3/I3RWCK3DwBeArJwLtmEhmpZmqGiIyM1SRJrKGKKfwBML+inVY6qN3CO92hXB2qmI6beTi+5Bd2rba/B16f7vDH0x6P9w94+PKIx+YB+6FvlFxURiREeiCepnHfN+2xQVV3qIfCFBgK1ipcFSl+dc6MXB97zRYLRIuTQDvD5szohwiN5GsM8zd1yM25rYDtny9+tKUz+7Wfh0XOU45TAWiAb3WoXmqrafDROFfGVjPlozPj0AZrqpC8a5haNhOZb0DdheMiRIc0lXpe1eGE+q4OXQ08bWORsVLjVS3p74Rutu0mNN9iXpDr+QvdvnPCobdZwivjc/vVid2z924ToqU6uERcbIKwF3HR6Nelll8iosKozHtCmTmbVP6M3Bls2FO+hHqsJis3O6C7zUdnviJq6fSyfQgDUZrozGGIxki1koxkvBtTgsN5+QnA0zphSvT1uvRXBVQ/VDF1dSwx9obVOfNy6ZTMhM4Db/B6f4c/vt7hj/sjtvcHNEPT7qry82Uk0iLRlzH1td2gPW5wGlta3eKl/gZsW3wahKaqu2H/FfpBaryRtL3cjxEbzRiW3ZQWvPa6yPWT/l10yxkMy0QyvEiD3o+OvujnS+IlU1X92TahemlK+o0lxoqMV3VZY7rufV3FVU2p65QTxtnzIapipEWOCWAms9Fbr+LnfV/hdNzgUPfhf6i+jUVGpvoa32MuMl6icBSdKXnTC1GZ3GfqfeZ22doAhLe/ku202ETXQA+BsFTNpNfZKE6JSHgnaw3tXLGhyLw3lJk3IV8ULTD2uaYzy3aYhEZe85cpaVZLjVBj6h34CeNAlDY6I4MP6iEoT6awCUeNC2jAjqA9/wVtm2Xr7aIqJhuZ0WKTegDx0A73mHpEvgew3eJ4v8WxBrANv6ptnzVVHTrTA4BT34wRmF4iRsdbk5R8A2y3eK0RmovXPfquVn3gQPU7M4/QFGE/w6WX6funjOskN/w/EQnSOJX5yiyz4iLzXlTMm9r9DVGZl2bofBG16vqwWZQYT5aBIBS9VDVJdMZ7f1pOgFFQgDiy4kVV6lFi+lFSdAROM38+RGaqGoe7Haq6R3vchJZ4T9tJZOR85RqLyGzV9Og8n0V0vB9FC32n5O7oa8TmUrUoOVFfOp8RW+2mZQXIi0ytlq0RG71vwQs9pbYl3xvKzMUoEZrcz3KJ1DwC+C1EYLyXbDFFZwapefn6gKdti/1dnAQs083QRHc3/HpuscE+qkaaJwPrAlue51oyjQWVtGLyRMbmz6SiNk/q/W2d+bHZ7g2wvcVrfYvXLfAi97tBcsLJ1UEO7XFkHmafx1tge4uXQco22yBFXaPHb4pziqJIlY3I6Mvliane1sOLzNTD1O7DyodeJtfWG20cap09DysT6jEl/drR1OMx3LXEpPKOpOO80N9MD9Q1sJUO9NTnWfdRVKV25CWSGBVV0dIiouIti87LXCQZLLVBi6Y54dDsUdd9iNI8qfGlnhB/djoyM/6vYS4y0edlWzbZ6qWFsZnOvbtfSmQ0a85Fi+AsUgVM1+GbegGQFpm1pJp1r5EVfVxKzveAMvNmcq2b9D+0/mJ5JZDIzC1CxEaFrvVmT97jBu2xweFuFyUBb6LIjLQyeR5/QQOYiYwVlmRuQ/QOEiNx2/uILZhTURt7s5c8oUhkhoc3v70JoX97zNTDihIA4BavAPq6R9/16Jt8q5weFb7Vwy3XRl5yURNZ5klD7pqKK2sB8f69dD6M3YfF/luKNMn7keeqT5lYYnTv0HrYiHkeTe5/KgxtMIyZMFQNWYHR0RUtHPp/cRKWWFxkmXya8rrKea2HrBsjddvwXg4AXrGfopKRJCP+bHOP6PPx7hUFEmP/f2y07a0sRVbsupLtZwJjls+w18FKjt1JCrvunCoq8qOhzFwU/Y1NNde2Xyz9/BlT1ZMzEGWNeQuKYf7laYfn+z3a5jHql6WZ/U5ux+qncFZxlMGL0mh0hYH+nd0MjcBPMs6OnLeWj2Q4XV0iGzXRz/V+PJmxz/Vl96qz9PQe8UcEAHVI9KzqLil9s+ukC359LkPVTPTcCowWHRnG4A7xcAZ6nxKd0ZR8o71t9DlrcbHL7oDjHXC4285ahsXN4KdhI+bDR8SJwTYHq6r7EN1LiMxme5olaIe3MEnL9FZjQfEiMnZdCVFEZ4j+vHYrqhz1/6gnNICKztQoGiHb+z6kxHjtnd9zAU+ec+dmqw07NbW3R/s99b6zSSSiJVKjq6E8Mamd5XIAisy1QJn5Lujwp/4W2ujNi3ncTgW53JRstc0oNLdoj6EJsx5FOw796xGvp8iMTG0ejcwL+mavQ/NyLBlnZ7Ntcdxupq7p5SYrNy/vv86KjL3Z1fB/3aZu3rXZt53qy37vnA+AUGUQEpqn6+Q3L+5RoatCC6LoHLyxlew56tfIbu8QR4wa56HfY6ocLf31LMfeqqkWmqHl0uHu0zASeSww0mJMxsKSUcrDuFhxhKZFA9sSrO/jN/BpEJp5UvY86Tucfiw08/l5BCYnQZbi5vg6V8zbnRcx0d8LLf1uFUtif0vRnpzY5CippVmSGu+77K33fnjAmc9NI3SLKG8MJy0q9l6seY96N3JpKDMXJxWdKRmJNVHVpG9EKhoTCc0TcHzao/38hAN2SlmmPnRtdEb6YglHngrrME3/yoxFJgiTjP10wgan7QZH6ZpeRxTkppSTDbuNJzHA+pt2l5j3nkcFSxiaIe6GP05mnV0rEQMtKVpqarMOmAtNSmD09fQiM/occsu83Bh9fkqYpHdf6RTPDihqBxqV4SLix24m1l5kqx6Sr8OTuchsqvmwFbmIjCc3dnu7rf08O1TRvouTvi1eIS5T+73QUpPaj5XRc0UmtTwXjVl6rXcMKzKyTM970Zq1UhOdoxUaTU5gLCW9BpMfCWXmInhVSp7Q2Nd4yzrMBqW01U3JR4PnP3fY3x1Uq5JNMjojTY4FL9pgl9uu9mpVxTT2PFz3uN2e8LJtVBIn5jdrKzReVAZqez2fkxq9fOlelSsYtgCON3jdxsmr4ZrEY1J1qNDXoX8XeNEZb96LyMhzr3pJoiZalFLvx+K1ftLr5P1mJEZaLGmZmURGd064H4eK8P7v4nGu6qE5/9AKDlPLI5mXHBktMnI2XkskKy1hvkx0ZF7/78v27jhc52D/T5emtnoGmW2XJKb0jr8k/Hp57cx75P5ftcTI81KZsa+dna/ts0ZvUOPyIsJIzo+AMvNuWKEpfY0dlPI2LuidiMz0XBKB92P0ZY+DaTI7iQ0yXfkLufB6GCLhNIhRPzxvsak2aLYbvNQ7RNEZHWHx3nouPG/xbuBw5lOvyz3X+UlbAN3Qf07ltF5S9PUnoH6Nz0O3PhI5sRJTO88bhGomW61UGpnx3qNMbb4O1Lk1aYmRvnqm3pM3rtSE9VMnhYdhW0kAjhLQ+7QgVHU/JfsOA2nGZ9SOAj2+xkRbwrJ81dN0mfr0ZztITYjS9NH+s+T+l7Vwp6RFIpp6GZzt9P5SEnPu3T4nNt4PD4+S9fY6LEVfbBRHz0fVdMC8qu4ciZF7MvmIUGYuhheBSX3LU3c4HZGRu4+JzmiJuZ8/f3na4XR/wKlpxl/JumpJqp5ShUCOuBlrCPH3qKP+bMZRuusen7YnvG5NVZMnNCVhZLss9avULtPTknX6MbayGkYBr+YJwFOEoZKL5IuJjnzkJEbmZVsdobFiU/rtlX3a96jPtwljLLVDK6W2EnnZRB0PanHxZKbFBk9RNdNujMzoSKFIjERjepM8K9GZ0M/PXGAmLW9hIy1LchMdxyyXqIwnNrJ8Gu07IzSp6piS/0lgXsVUKjJ6PvU8dY5Ly711uVwYe472eep8lr779qElRpjlHekDpt5gqeCsibxQfr4XlJmLkhOakmQzERkdnXkOr/fGg3EjNWGIg01zwlJ0Rt/8pTC2N2gvP0CK836MxkwDW45xn+YUEoFr1feGFOheHkDqZpVaJpTISW7etowaBUY/bkLeTJMSmaHKqa4AvMZCovdtWzSlJEZHclIiI/MWr3xdEqc6JPe2TWilJKrwPEZUpmEgUgIjwhNaMU3VTlN0ZpKaHnVUrWSRvmO6rgpRGVMxpUWmGf/30hGZpVZKer33/96jTubTuJSWdfr/0JMBLf/6NVhYlpIZ7/hL55sSCrsPvc2S1Hjna6urcsf2Hno/R/U82o/uVVk3u16TO+NBYfkIUGYujjfOh/dNtV8gLT1KYlAP8/sFicEYqXk5bnBqN250RouNlwDZD2F0eW6Jm7JO0RkZ9UnG5T5gFwqlbQtst+n+NjQ2RGwfs19aBu9XYOoh67dm3pOaYyhYPZFxz0GLgkiHiMqf8KuabGKwbQGVahG1dA06ta2Tr/OtxjBQZKhWejZyYse20pKT2kZERicI68iMDCkxnnLty0atqpek+b8MoioiE3q7ziX7riuk9NAVGi00xfvMBQBKlgHnVTPZ9d62XlWRZUlk9LqSkqTk3LxbpT7O0sM7ziw6c6lxlt4iQOTSUGbeDSs1nsR4QqNFRiUByy8IT2TuzfOnLQ7bEJ0J8jIlYE69AofCAMAYYelUsqPX0kkIxbiE3KdWTHpM7mboc2ZMBK5NIrDGExY7HS/TN0xDQJgomL1pdUBUV+5JjRYYLVzm+NI8e96poKp4qyp8q19wYyMhsn+dM2OFxorGFvMkYFtdJSK2JHg2GtNMEtPXcbWSzX+xy1LRGK8aSicFj7lafTNEZcI/QVV36IYIjdubr4rGTEIzZfBsTGQmvOV8gm8uupJfd8HbZeq7kNvezi9FO+zUCkNpWZyKlHgRlZSolUSN7OvXyoxEZGxEy32vcu99a1TGQsH5UVBm3p1Uaya9Tn+LXxCaZXfquRqkMlXwm4dEZw7NNKTBJDZTuH/Kg5HWG3FnZNJmyRLHJ6q4immI0oyJwNsNcL+Nz1sEbGvmF29Etv4px3DNUANQ/X/YG6qVwy1mcvPytMPhr1NT40c84B4PeMQjHvGARzyEPk/+8hX/gWOcbigi4clLSmhsFZOVG9vPjHc59C9VR2L6+hP6OkhY6MRu0lzp3C6u0NkoMWmiZf420zLZvu8qtMcGL8cNPtU9+qEqCQjCqFsyAcCpblBXvfovs23plqt9bOskSzxsxySn4RrMO0iMk533eP5zh6evD2F8pifME/PdqkvE/8opPGHR80tCk9vOHsdW13iyArOsRFS85ymZgZrPiY233JOW2ev0fVWmL846eXTIR3JYxfRRoMx8d5bkZoe50Mhd7xvGTrm8CEb0aNAeN2rsnE00VpMUL9Vwqwd0gm81Sg2AqPCwTLkzqp8ZbHDCCT2e0d/V6LoKx64CuttYZKzEyEPwcmtm12tpvdwVdxijNFpq5DhaZDyxebrF85+7cciIRzzgYZCYRzzhK75AOnCLhCY1VIFXtaTnrciY3Jlv2yAlAPQ4i7jxqgbqnMTUSlpCs2kd+/Ck5WRkR3rzTW8zjGnVV2iPm2Eg0mEML9U5HoBRbkRsqi6c5wkNptZEWqHXNZe24jLNT7JjJUYPmqlziA7Yhf+Jp/1cZEqlBmZeSEUrSuXGE5mlu31KaPR5pM4nJylWYlKv8WTJno++J6QiTEVCI5Ji5SZlmHZ7D7uOovM9ocz8MOSbaBPQtNDIN1XLjWmqnRSbG5yODU73DdrK629mM2qLlZgG8YA+Nj9EWnToHAIRGR0F6oeCsb8P1QovetBHubFroakR9xQMTDe76D6hbxJdYrm9zkAUpZFVuTykrZp/Ag5PezzePYyxGInOfB1SYjdDtR0A4C9f8VAdQ78z8kjlyngJuro6SlVP6RZHXVWh7ofPoJPp6zANr6v7vMTIZxu3E2rGQtyTFmlebQeYtNEYGU17lJ3jBn1Xh5HLpWO8rgbqbhQbyzQuVj+ep/z/yTIAUa5XipS86KkcI36vMmJ6HTVPf/rzAYenfRhgcq3ErInMrBGalMDUKLvbexK1VKX0VpHxZMY7B+/8SoUGQKii1pEYKzB2XSoqQ0n5iFBmPiS2gK7VtLyq6fVpj8O2xeaz3xuw5CHoSEwD6TdmGpTPG6dJ5yiETvNC6u8oMKiwwyEUNhXQ31d47Cq8Hu9iibGRGXmuq4MivF9Tet77daTzloZrKFIj187KyxYmMhOu5/OXPZ6bEJnZ44AHPGCPZ3zFl7EwHVs3ff6Kh/oJW3kfkgisn9uIjCM0EoWRJF0pVDtUqIdxgcJYRT3QDBE2JTlaYML5xU3KZX8iH1pMZF0sLV6VUz6Kc2o36LoKLyIyxxv12fpi89pVqOoOp3aDqukigQmt8brZ/2WOnMDIfuS6TkKzmcmNVC2NIvP1Ji8zel5/ZwVbiNtC2ouspKIjdppat8R7icxamdHHk3uDVw2dEpoutUDLilfNZF+XGrsp9Zx8bygz3wWbG5N7bmPPTiza/rJLRhb86MwBO2zQjhqjpQYAphyaEKGxhaCghUb2J9GZqaAMPc/smgP6+wpPxw1wvPWrl+SRFBl7TWXqhYoFfZezd2bVh4+NyNjpEcDTTYjONA9jW51HPIz94UpkRl+n/q7CQ/2Iu+Y1Ly5mXppK9/VcYHpV2OrcpfCZDDEXkZvh47ItsHTOia4mOmGKvGgx8ZZp2YmjMCaKMyT9no5NsLKjyV/qHLEZ6Lt6jM5ogenH6Ex5NZOWGD18R1zFNEVj5D1Gkas+RJiyIuNJTC5Ck8N+B9YIzZLI5L5jS9VN3j6WRCa1XO/bi7jIVIuM/lp7IpNFy0nq3uFVK1mpSf2AAhi9+f5QZj4kEoXZDc87texbKAxSN0cbnTluws33bgcdnZEE4GaQEN352NRCqRljNpYpf6Ebn0sSsE3QnAr2ekj+vE0n24rM6JvW+F/6TV0LfSOx8ymZsRI5LLfn402Hx8vTDocvezxXe5X4O0W54gTVoVBsNuj/+hUP9QtuTOLvUi6Lrgayz4G4CXKqBxxPYOS57gQwVb3kJQWn8mS8KI4k/b52VbjW+vOdFVqD2HS3QP1tGrW8j6+LSF2LzdgqbwlPYuyyKSol73eqWpIm5adjg9evd+kcGSs1uWomwC8Lc3fmEqHJyU3JcTx58eaXRMY+L5EZPW9FxXttkcjoKiZbhWSFxooIxeQaoMz8MLzojBYYqGXyGLbvnKqm5GPoRG97GqMz0u+MFAJTom/cpFVEJ4XIT40+KmBsjo1wwgbNtsXL9gXY3s6blEveTA2/w7DounjhYu8G5f2Es3lKTpN3L1LzBODrLR7vH7D/6wE7HIbE36m/Eysz44CUVYX2L094aJ6S0hLOei6CttdhkQ75rDyR0c/DZ5uPStjqJS0mflKwnyejq546VKMA9F0FHJu4msX+yp9xAwzNuEMPzPpd1WpaNrSAvY7eMref4SEaI62wssm+Vma8quDSqIwlJTH6+ZLIpF5nn3fO8/cSGSu1qXn7AyclaELyGtsfP3YIGSB9P8nN2/2T7wll5ruzdAdXuTHuXW9hvCanMH7dzqMzz9iPN/EQTanHxF9J5gXy3cHbfjykA724xcg032KD9q6ZojMiCrnIDOzlsmFeKzI2OqMFRpKo9TKE5V50psYsMiPRmcf7B+yaAxqcsMPBjcx0Y/RiehzuduN1WSpYvWVabuTzmUaRnotMgzaSl+nYcR9CtnpJR2jsMl3NpaueRHrG3BtVvfR63EwRRfv5pqoWAMhQEu1xE1o5VRVkLLCcOHvkBFFHvKIhHHQ05mk/vYcSmbHi5uXM5G4HJZEUKzhLIrP0ejufk5rU/teKjN63fmihAdJV0N77mjmGvZfmojL2xXYbO08+ApSZ74bXJNvrZyaFKaS9G6WNJjjRmUM1Nc+e9lzNQvWpqExqHBzZXr/Ovn4s2O83+ONpB9wnhMatYkpdCxnPyi7TnRZ6Pyv1sh2i6Iy+fiI1upD6eovDfcid0RUsUxq1FplHTDkYG+yxH69NLncjt07LjEiUJG1P3fv37mfhiQ0AtyoplRRsozBxRGO4IiqaEVUvpQpz7yvQAahv8KqiM101NcmepKbsNmZFRhLV46q2WGQOT/sQjTk2wJMjMjmxsYVzKl/GCkNufs3zlMh4+06JjF7ufUaXEBkrsvb6yPK1kRkXG4Xxcu68H0W5/Bi7f/IjoMx8GOy32FYxyWP4stjQdap6pA7T1zq0bKo+h67gQyJl5YqMRldV2G7c48TTakwCHvtbMdvKMAeb5oTb+2e8PN36kRld3ZS9YXm/ouwNSpNa9oIxF8mTQ3n+NC17edrh+X6PQxOSf6UPnxS9IxElCanhDH0B0etss2SdENxjudmyjuqc1LL8e7HVPUoWuioMAaGrl7wC3RUYMz026Ot+jM7UVR9FZ6YoorTLC51BSo/W8v7n/cfM+5MRSXv+cxciiE9D1E6LixYYT2ZyOTKpwrqUNZGVUqnx1mlSX5uS/ZdEZLTQeOS+ynqaur6zfBlPYuwONUvJwnY78iOgzPwQSiIxsp33GDrPWxCYqKqkvsGxfgjj3dyFL2GoWgod3O2dKg5A+pCJC2ld5aSThqW6Q6N/NYfckmc8Yz/kznwDtje+xHhC090gjm6t7YpcR2v0MrUP73K7EbDbUP3QTP2ONIMkVsPVlBHED0MelJaDlKCkqk30ddSCYkcxD0nYU7FcO8f0jjvlTdXQo4DbHBUtt5Yo32eIprx2FcaOHt2CBr7ARNGaG7zWG/R1j9Nxg+oulmuRZx011D1aT+95LjF2PmqtdNwAT7d5ibHL9HIbfbHve34BywK0S9ESjSc4OeGwr9HHye2/VJJKRKZEAvX30vt+RtdanjybHXhRGph15FqgzPxwUtVPtVquozRSrbL3owiRwNj5WzzVD+HXbTMlhkoh5xWiG4QG2jZZWEdlchEJeU2IzIRxfHY44LDdzwehlHPV85HMyB5VhCqaL0VvL1+BDrM+fBZupKdjg/Zz3FOuFPsH7KKCPyzbj0f1oiu2U7dcNMXmK02tyeaDC8i+ZWjHcOyTmvdGS4/lZZ7eHScXi8R0qELLI4nKdFW6IMoJjExlu+MtXmS8plr607HnHLfssu9L58Xo/J9xvq9weNrF+TEpeclNdYEKM/UCAKW/bVLM5M9QEiHxpKYUT4JyYmOnS1GZpR8XnsTMojLP8CMzS53kLfVBY7cjPwrKzIdDxEXPy7fd/GJYSPz1fwVt8Vg/AF+ArpH4gZ/jIgXBBlNB0aOOfu1PhVs1m9qIzgYtdngOVTJVOwxCuS2rYhr/U2VmKSqjby6p4SOA+FfZgsyYgup1GP+qbaauCCUyE87UH+hQsNc79dyKyzTfjdd/ns0Spvvx8z0N79ZPzq7U5ytVN1MS8yaKAGm8EaT7YZyFEJWp09czRVJoGrwAaIfxm6om3skkMFUkNtP7nfrqiYSmr3A6hk79jk97Pz8mF4HxlmmZQWL+rRKjKYnQeA/7fZPnpXgCs7QuJT32f8KLapXITXTtvSiMXabvFd5y759VL6PIfAQoMz8MffeR6IzIi3zZbEd6sm4ofLvbvMAkfnG94g6PAPAFQ1fxfmsQWwDvcYiex41j+6EwmfJlbNVIiMxMzcLdqiYbnTn312JE7mbjiE7xDbM2VU22sk5HMvbz44yH86+/bUFmRUaOI9VLtYnM7PEc5S/p6Ex4HsuMPmPpvTmIzWnsc0jOKyVnuooJtorJ/eUMP7KgRQay/Aaoh873MERoqimKZQVGD6Aq5ze1yppaW7XDMAtRoq8VmKUojLdM3ocl996XoixYWCekvjueuKQioan95o659jWyPicyMk19H5P/W98QIjJeVMY2FvCiMvoEkFhPkfkoUGa+K16Vkl5nPw5dlQJMX7hnuC1wapQJTR0Sgh8B7O8PYTRmxVQgD7+Ah0iLzaeZ1kl0AJgqpuJ+a0LPwNLmJfTL8rh9mKqachIT3VyHXnvHO7rtMwbwS5AcppWYTFORmXH+ZqxqkpwZXZW0JDFL9NFnMFXp6JZkNjpjRWb/53HaX/1JzZvPsZpaYm2GqIyWmA3aKEIzP9chuhdVMSWiMrrgsfJivV3mR6m5xStCnKmue2AL9NWUvByuy5Qv40VmdDSmPQahedU9U3sC4wnN0uM9yQmNt9xGOe0PB09ocvt8S8mR+npaqSmJyHg/MgBMImP7kbGRmZLuHYB59IZ8NCgzHw6JyNimxbLMCI0kAmuRyQnNyA1eu7sp1uIIjUiNHrF4A4xTHYUIYzohEhqJzEjkIST/HnAYBgOYVTW9S1RmCX2TUqOShxNP30CHh1Q1nZopMmNzSoSpsF2+GXr9+oTXxgV3HJ3pxnkRmaYNI2l/qwG0r+N+uuo13vE9cKo2mNKW60E+N0OS+Fxk7PvoUZUl/upfz57Q6Hm55oCSmiA0rcqhqeoKJ8gYVfnITNR3jLS20r1q52RmSWRmhaoiJRklkRYPLXsprMTYyIxXxbt19puaXzq33PnqbbzojH7dktTYaJ8rLfKwkrOU7JsSGUZlPhKUmR+KvYt5ImOjM/pXxpAILDKTExqNev6KWGikEH42hbLui8MiBZwWmjhvoRqkaDNGD5ohevB8f8AfXx+mqibvRqvfR1er67J0Myn5BSV3UbWv0pvnEWOrprbZYINNMhITR2k2zvp5MqtuSZbqtFDLk0jMBic07SmIzJ9h25s2emEcH6wB4AXd51C1JPJywmb4P/AfHpIvk0z8nVUFIC80+hwjQdAJwV2I0mASG2CK2Ai6WmnqyO82FhErLqnoTEpg9PPx2jrkBG4tKanxBGRJZuRhX+PtRx/bOx/73E5T2+vluUiMtw5ALC0HTFVNtsrJHjjXv0zuRMlHgDLzw7F3MJsADMTVKFLFJF841UxbCw3Uc6jnDiI0Vd3j0OzGwnGDE2pI52uxyNjWLULoBXiSH92/x9RBvOosvjnNWzXlqppm95NUInDpjceGYZwk4IXC6/i0x/P2hKY5QY8cpOefM5GWaX4655D/MlXVSUWffp13nAYnNH2LzfE1iMyf0cHm1ABa4LYD9vURp7swcpf0KLxR8ymJkU4Cx1ZZUsWkL2tOZmS73C92N9IREoL7ukK/IDYA4iEJpCM/L+8l9dDrbeFq/y+sYOgIzFI05lypSeF9j1I/HO6Rlplx+s0555vp3MdlzlRH3ID0e7X7SUViZv9PtvWSt6GX4OvlzeREhlGZjwZl5ruTy5sR7J3QfqmeMeWO1BijMxodndG7SvBa73EYCoG6keqlTVSg6ZwZndybqhIRRHB0zowWodvtCS/1Nl0tFp37jZpf28/MhfAKY8wHbrSDOOpqOf0aLx8GCBEtT2JsFVMclWmxOb7gtkU+d0OuqYrYNC2wuWuHCNNu6Fxxp1QlfriXpqtC1Y29Tku/zlPVDEe1blao3QBo8Fp3eK17fKr7UM3liA2ASWRstVJKZvQyu84KjBe1S0VKlmRlbZVODruvlNR41U1AEJeh9RiGa/mpjr/vrzoaB0wiCyCqahyXmXPzxEXP5yIxsxwl3ZeMl/SbExmYZfaEyUeGMvMhKPl5IuhqkcPwfBCbVHTGm9pd1jd4qXdjhCbkHoTHAXG0RiI2J2zQoI3667CFuH5+UkWublESfiljHv2wv7o6IPzyEnK/juSNivDdmvmdmX8I8/Lr9B7AF/P43Ty+APj9iM+/f8WX5ise8Ig9DtjhGQ3aITPoeVh2iFoX5QRwEpQWcd8x0xCIU8JvO0pMgxPqvkfTIkhK3LgsjszYf7ku5NbUfehhVwtT6nz1unF+kIpX+Qhyj3hnPqV3qK7CKzAKTVX3oUWVYur3ZiGXxxaYqf9NIF3OpaQ8t9ybt9dgqVwt/PGSfY0XgVEiU9Xz/4XXrgrbdFWQn66eojVy3mvk5CkxTT0ATFKiq5e8qIw+KRuJ8UKGFkZlPiKUmQ9Dqf1LvohEZ2Q6rHtSm3pSA7M+eoQ8hMPTDpvPLaoxobUfWracxiEJbBVETmLk+dQDihrRaPyljOVw8nidUnXa+o3pXCMRGS0w9fBcHvuwyJOYL5jkReZ/B/D7C+6/POJL8xVfoGVmGoByP4pNkBpvqAcP6U15kpc+GvZRJGfKQTqNUZkbWxDrS5f6xg/lU9WF5s42AmTPbWk+/Jq/LZOZpSiFJz8eg9CkcPu9ySXyljy88/XOPXcNUgKj5ztnHolzSJE6Z+/8RWTqbiYyVT3tpO/qUW7GCI13XHsOXkQr9bCRMe+zAuBXL9lm2UA6OqP3Y0+cfHQoMz+EkqomD12QyxcVZl+3eYHRu7LUAOotTnWP5+0eddOPiaDtkNway83UUVxOYuR5G1WGhGhOJ7+USwuUYuywB1I6aKkZJAa/hc10VCYXnfmCIDK/f8WXuyAynswEebHTucx4AyXq0bBFAbXQTFrYDtVB/Zj0C/uYduoLjSzvgKp7RdX4nfTVCcGJqsGGAu+17oH6Nh+VKLn7pAp9obsJC6WAHSIEbsGqozLe/1iucF0rNfb87SOqyjHzqWnuODlRLcH9sZMWGV19JxGwMSKnr72OYHnX2ot8edGY1HwUlXlWU69Ztp3XJ2jnUzAq81GhzPwwloTG+9LoqIy+Mx7m28mXXuOF9p0brOTP2OomqVoKWRW60e6UT6OrnPzIjO4NpQkdoJX+Eh6vS+6Xk24BJs+96IyIzNBfjxWZL8NDz48i8w3bL4+RyHzB/+ABT6OwjP28qConiUcJOqHazkvkS1o0aaHZRFqoozKvuPGqRuSzzhV4HaCcZJbs6yX+zhOSzTb1N6C+SRfmJeSqTfT7kSqlupsKUys1XZ2WGC+BNxclXKqN8CRMS4y+BktRGf1e9dQug3nN0t3dux8AiPJkEIuMTq629EuRmZzQLEVlUvMA4s7xtMhINZOO1ni5MKVRGYrMR4Yy8+HwOsrztnl2liu627nMyO70jdC90Yb8mdO2RXu3wcaRF13tJF3Ee4P3abFpowqR0N9HaB6Lsl/B7nXysFEZHZmxIhMGgVyMyvw+TW+/POLL5xCJEZGZIjOxxOjIjEhNycjYOjIzCc1cbnRT7FpyZHTOjC5IbMGoaTBEZpYlJbdeCr2+rvCaq2oqJRe1ELqbqVqky+zc5srY/zcvMpMTGsD/emrB8L5feuq9L+99piIznVn31jt6dMzerVqS6FtSXjxS3+lSifGiNR0w7+XXVjPZKiYdoQHKq5QoMh8dyswPxUZnbCnj9W4LTJEG+dWRICU0ejcynf1qvMXhaT8OStkYebHRGS8aYyXHVjG10s9HSUEyXoKluL5GS4zJjxnnC6Myw+PTlz/x5a9f8R/QUZnpuc5z0RIzyUzcc0t4R9UoMbJcIjCT0HgJwW3cFNv7pSvVTDmRkeUDU4ulFRKjntd1P8WfvAK99OOz+8ihk01TYlMiLp7gpF6XOtdSkVmSGT2fu2aXEpnxmN1YvRQtHsUmRGxThKqmxMksRWJSQuOKDDCJjCT86oiM16tvTmRSYkORuQYoMz8cr7pJR2dS3fVL1dIOk9DoXyPD67ubudCkIjPm5vta79FuW2xU77ZTd2pBbXo8z6QlVeUkRfoYvZFkzKUQf3RvWeobwuPWPORNDtfdi8pokRmjMkd8+V2qlB4jkfkr/okHPLotj0Ri5HmqGg6YmndPo2BP0ZiU0IxNsXVERkdpdK5M6hs/RHFsGWb7l8kJDoCxkJvyZup5VdMWqopgAVuwz6Iy3rIbuKREZq3ELPm0V73kiUxKZrxlpce2rxdSEpuJltnqpSqqeu6i1nGdjdLUXfgxZc/dPs/Ji47K2HUA4g7xOjNvozRL0ZjcMnINUGY+BLZzPPsz2gqNrD+Y5/bLCgAPsdB0CAW1vCz7y/EGx6c96rpHc9cOPcKGMYimCo4w5tIJfq6MrnLSIjM2yfaqmOx0vBFKU9HUTcZK4a1a5kVohkX3SEdnxsc33H95xEP1GImMVDFJNdN85OpYalIyo69ThWkkLC0vXrXTLOk3VV23GNkA0E/NsyvTe67Gj8pMfc/MqprqN1Q1lWxfUrh7guz9n5VIzPHM87ciUyIzJe/LO+Y5jOfZL4rMmABehVwZyaWZVT15ApMSSVullHoAiBN97cNGYTyhWZIWisy1QZn5sOS+TLpq6YCpwJYv7M5svwu/kr5iEhkbmbG/Gp9kvkF7POGw3WNThQK0HaqWdnge571cmU4JjKwfU1ZzTbJTj/HEPerEOh2F0Y+buchkhGb7+/+YhN/54wGPs9ZGXgsk71ppIZT8GRuNsVITVS/paiX7WJHagG5qnr2EnxTcYTZcg5cILAX5koislZ/UPlMC4xWoueiNFxmx55aKzHhSY1+fmpfj2/e0FLmy278RPVo7EHK8dP5MlWpJJsfPiUwqIqPlBkBaZGTnOZHhUAU/K5SZD4n3U9ouy335vD5oagA38Q3xCbHI2PkaQH2DFzzgEQgF+3Cf0tUiqaRfW800Vsq0X/D09QH4ehMESx5PauqGlVOtEjQ6OrMzj98QOse7icXl9+HxX2o6Pr7h/r/+gb/e/RN/xT/wO/6Jv8KfD5GZuB8Y3Ttv3ffYHF9w2t5iU6nqtkH6wvWsYWWgU2YRhCd89n1VAfcHAC/hXdsCsxoe+rldL/N34XH8DDw2D3jGXvUHFB76s9VVY5aq7lHXPXqpagLm0RkhdQfSQUj9mnPkJoeNinjT1CO1D5m3URj90EKXkqHUuQLT7aAzy5aQqFL2Gm7xWn/Da93hxURpTvVmjNIAYYiIrqviQTtt8/cnZ2rnz4rIpHr4zYmM3k/qwjEqc41QZj48Oqcmd7eyeTXSfFtXRalRtmWRRGE8kZFtEAtNX02FmW7NpCMLXhLwIx7wz/av+OMfX4B/bIF/IJaZr/BvblFUxibxaeTurnNjRGKMyHzBFH35XT3+D5TIvODzf/0T/9n8tysxv+Mfw7Iwf4/HqCpI5KXqgHqowkELbJsXHO+C1FRVaO4uhEBKLAq6HxqphjqhwQYt+qpC9/mAZnvC/s9X3NQILZMaBEGRsZmWpGYLHP8CPN7d4xEPOGA/Tg/Yo0UzTiXCtiw1Q1VTV8XRma36+HKBNj2fEobUa0siPnY+Jy76vHMRFb0sJzOpyIx3fkD4HmwxfR9q+EIjWMlZI4Adhs/qFqhv8VoDr/U3vAyJwbfbU/hsuzoe52qp+mhJaFZFZOwAkjoqYyMw9s1RVn5GKDMfCisutbM899oXYByZuUbcqZ7MK6GRm4e+WXs3vQ5Ad4OX7jd87Sr0Xyr0ja46iquSdIRGr3tsH/Ii8xX+uDidvD9909J4o4sP73XsW2YH4CauQtIy81/m8X+O+Mt//RN/q/4b/4m/z8TFi848/HGcpKXD1DTazt8B2w5o2hdUdz2qZno/4VrFYzdN8/EHE671cxDJ5oS+PmDTvITRsqXga9RnKrsyUvOtBtomiMxXfEHICnoYuv+bHu0YpWkQOkHcqAhdfG5V3aMbhhWIojNyXlDnoklV4SxNvdcukRMmT2I8qcoJTUl0xttP7lzlNSmR8Z5726euW4fwvZjJ3CQ3L8cmiE1XA8cbX2L0Mk9kPImxy+Q5gLnI2DroXLJvTmAYlflZoMx8aDyhWbpjHxCExn7RNYPQaJGxEpOQmtfuDn8MQtPeNXjAYxSRmUVn+moMRR+/PgBfb4PI6MdXpEcpnlUx6T4jgGgohxlaZFQT7C/mIVEZFZH5X/+fIDD/ib/jb/jvSF7+EyZS0/4Td39/Bf6YrtP4Mdm+XmTaAzc9sMcrgBP6poYdVBLjLubRD2nx1KMespZOaKsNmrsT9lslNXfDCwZp6SqgHz7Xvv6Evq7QVeFzE4kJjxCheUKocpJqpynvKUhrZ86tRo++6uOk0HoYZqCuMUbGUpEZW8iWSINHKmJhX5P7v89FZ7zzsPux1bcpoVk6R3sdtNB4WHGxQpO6biIyejv3MYiNJy6p6ExOaHJTAL7I5KqarMjoN8g8mZ8VysyHI9f3jF6X+wVhewT22GEcOdiTGjmkviGOjy2ejg3aL4843W9waHYzcem7Gr2MoCz16E+Y5OUfZv4r/Kqm8b3arsm9awLE/9LOuEs6MvMFcRXTIDJ/+T9BZP4f/F/8bZCZZHTmjz9w+3cAf8ckM/q6AVHPuugAfEaoT+pCFCcIzSGKdtmm2mG+HpfJ+s3Q989mqHgSqanvemzuQpIwAHTVvC+bTh1DZCZULwWReR6qmKZqpjDCVJwbVbuyJTkVEp0BgNdtG/4X9MjnXsHtrUvNwywvKZ9SApF7SJXQksjoaUk1U8kdWLax+S5WbPS5pSI23vXWEtMhlrDcIycztpopJyypdQDiTvH04JGpPmU8kfGqnLx/FEZlrhnKzFVgC28Mz3Nfvhfkf4YBwD4ex8nb3EZzx19cN3g5/oY/7nc43O8ncbH15zbc/BXLVUzy6IB5V+W6PwjvZ7bXFBtlUZnfgc//9U/8tZKITBCZ/wf/N4rE/A1/x1/xD/zl78cgMX8H8N+Y8lM0NtCixkESbmpgU71i07RosRkiLn0kCF7TdwCqefwJB+ymZOMhd2dTtVFVUEqSwgCi+7F66Unly4ThMndDZGZqeJ7Llxk/EdNxzeu2Df8j0qma7RPGSrReNtu5GtVZ9rNGaFKP3PqlyExKZlJyU3IHtpGSku29eX2usk7/L9rnJTJTEp3JSUtqHkD47j9iniMj8iLzB7XM6whPz3sXRa8j1wpl5kPi5cjYO6jc4c79EnbhGN1tHJ3xNrO/tKJfXbd4eUrIi132teCRFRn9i0ywJYrM32Ls6TcXlZHp78D2v/6Fvzb/HCVGROZ/4/8X58n0/8Rv//0SJOb/YhIaLTO6jLen1qv5GkAD3DZA3feoq7gPF9ux3th78lDN0wxNukVgTjghDAg6dbQHICkxslxkRsRlEpndKC9TA/ONkqp55Mh2qGaJRraOCtfaX15C/W0uRueyVIjDTFPLzo3MpKrHPNHy8GQmFZmRdRKRSeXzlMqMJzRWWHIyk6xa8u4BVmRsZAaZ5xqKzM8AZebDojvS03g/teyX0UuG7cx6GXDxtxBJsXi/1vQNR8vB1myXkxmJznzNPAcw/1VmE4C9f109qKREZUwzbBudGUTm03/9id8/S/XRP/CfQ9XS3/B3/G/83ynZ989/YWsl5r8Ry0yNebKtXm5/+TbhGm6OQ0KwCedokdHd8UlrsbB2H/VHo8d2WqqyAkKER6qVrNjoKqa4+z+JztRuhCbV5b0d1+dVDQwJAFgz5g+A7HhMOZakxYvKAPPPD2Yq82tlZqkqqCQ6Y7+3dpmWkC6xzFZnAf71WCMzJRIz3qK0yPwbcVRWy4xN+LU/dKzIWCgxPxOUmQ+PJzUlURn5cnvIx66aMB/Vr1r7y04LjHdzkuVLMiPikpuO79n7VWYTgK2EaZGRKeIO8b7AbZL95fevYz7M31QV0//G/51yZv71hBstL1pqpJpJbvS5Pl3kVBtEzaibFtjctQAeUKEbk2ytyIhgTCITNOUZO9MLzNRTa0pitMychubXIjMyFdHRIiPn1RmJ0SI2RmpUVVM9tHKKXmN7j5Ucm2Tna17X+fUUnZGCVqYedl2JxEghDzPNzS9VNcn2np8vnZ8+Dysvqee18xorJvJ+l4Qm913Xy5ciM1G1UqpXX9tayetDxou+5JaRnwnKzNVgC3AtNKnBKAFfaLyP/SEWGjmEhJ/lpqSFRkTmCWU3Np0386Qe8hxA3FrBhpf1HRqY8oKAOFdGtWKSQiMVmfkC3P7+b3ypQpd+f8U/xx59RW7+E3/H//r70zwS40VmRFI8iWnUdDtsfze8pXZo0g3pRTegE4K1yByGHBaRlvDOJ5GR/SzJjMxLSzSRGCs3VmSmTvRq1ZdQ5QqOEPcUG865V1EVKzWfbL6NjeAIXRULDYAoyVjY6teo+RpTgepJg123FJmRqa6usREV77n9EWFJRV28CEzqAcRNzfVUZE1LVkpm9DmlvvNeZMbKzCwak+rdV68XeYGatxeBrZZ+NSgzPwVe/zT6jivzOpIhvQPLTaIGsFe/khRWaraIxcZWM6VubnIDezKPWT15h3lfEvqXV6oFkzNkgf0lfI+Z2Dx8iRslx8MU/A++/PEE/AvAPxFaLP1rmMrj38P0T3VtpFCo1PWRa9mat2bK5hD1mJo9S58uEpmZqnxCr8FaWjyZ6SJxqdX8tHwcZsIZXWoSmmas2uqMyEzjbg09GffVKCUy7cbn8W1nNp5PAis3wpiD463X+z7exmKemi499FfLkhMWjRYkWw7b+VxEwzs3T2D091pHaGRevscyXxJ5yklTidDMRMbKTKpHX/1GUn3LpGBU5meFMnN1rO1YLxdeFYkBpojG7fzGp2VAJEbmZfmSyMg6T2jGc5P6cdvksuRGJXdaicoM7ysjMfgCfPryJx4qR2DwP/h9SPa9/TuCyPwLUxPsfyEWnH8DL38ijF4tERj5ePphmUyPmIRGd6Zn0Im/U57KlNtywC6SGH/ajfua7z+Ozth8GE9svCTkaH6QGCswwCQxOXnxqpZSEiORnCqxPqybLuzY5b60ptIdvpWKjC6EUz/4rezYSIYXQbH7s9t4EuMtWxILOR8ddbISk4o42flzZEaeA4irlfQPGHsP0OuAfC+/9v7AqMyvAmXm6vGEJvUFth93jbiXYGDsl0VuPDr6ooVGP5fdltzUnsx0PO9cop9347JVTHoIgxrRqNhWaL7I9Bu+/D4NEhmiMv8Tic1v/3yZJOafmKRGSQz+AA5/AM9H4PYI7LahddIogvL+m+F0RWR0hAaI3p6IRjdKxlTtIyLzPPT2bAUmzJfdxHW1k65C8sTG6xQxitK0G/RdlRWYZB7MCrS8aFmpHbnR8832NJ5f1A+SyI3tGTtVPWI/L/sZapG1IqMjMfZHg7cvmZZGZ0pkBpgLjVe9hsTUk7NSoRmRHyk2ImO/9yVVS57UwFnGqMzPDGXmpyMVoQHiaIfeRg93IAy9BH9FHJrXAqPnvV+b3mNWV27HWPEExkv8S/X8W1jF9AW4/fKIhyquWnrAI/4jDIeJL+3XIC7/jUli/sAkMsP05V/Av/4MZ77rgZcO2A2PG4nGaORapn6VD0wthepRamx0RkiPYF2GlphpfqpWapXQzJqKq2hMexyqmbzWSgBWtVRSicCfjKiIxGiBGddV85yhCn0Yx6qpogiSnKsIzouM5r698UVGZB7I/5+nqphSZW5OZkqrmFIyE0mE2fcaibHT3HdeL4+On4rG2Gisjcym5EULCvNkfmUoM1eJlRF9Z5L1JUgU59/we9kdhgGQG6VODrRT7yZmlyXryb0OsbxfYvZ92bwZHZnBXGIiofnm5MrEUZm7f75OEiPRGF3l9Afw7Z/Av/4IT5+HK7brgd2fwG9dmL+1v4pbhOoleZj7rh2Z+jRGR5qoV96DRJ9WUDvSA2AWafGiMXYU9FPfjBIzi3aMO9bzC7eb2lyIroryYLTIpCRGBMZr0aXfJ6ppwFQ06v23G7THDU73DV6f9oh6yRaR0dUlnjhooQHmkQyY9TmZsT8C1uTMzCTmmzrwzfx4+lxTU+/9eOc7Q0ditLDkorC5pF9gfn/Q67zjk58ZyszV4kVg9B1SnueQYQ92iCMzWh5Uny0yJAEwOUNOZtxTSLVasDc6e8PK3axs3o/qDTYRmclFZR7wOEVlpMWSFZlh+q8/pkUiMw9ytm2I0vw2nNV4mrbgEakxxC2ZNuNDROYRD861WCYlNCUJvt6YW7MqG2AuLksd2tXf4tdYsRnQ1UoiMp7EeH3u2BZdYd40UW8aHJodTvcNDts25NkcN8D2NpYJkQUrNlDPgXw1kyxb+hGgj2WjNFZuXKGQaIhnLbfx805JToqiW4yIkxUU28md971PNb32JKYEisyvAGXmqkkJjZDLn5HIiwiN/plYY7rJyMjT0vppEIZODTSXRG5ocq76F1iuntwKjH1fL8AsKqFzZpAVGXyZR2VEau6H6RiVkYeIjLRc+jfw7z+Af/WT2zwjiIs+25cewJMSGiCOyOiCRzlGp8ShVVEZiZjI4I/TLufVN7oVkxYYWyUVxulWEZeFgUN1lZKbdzI/kTw1ptdJ0+pIbOLzrUVijMhscILu+TgWmnlHhN7zFhs0aNFWDTaf2yA1TzuctkOkxqt+kqilfBfkuUepyOh1S1VLVmZG5Ptnv1upscxuM8vU8g4IPxikGbz9jsu8rNNyUlKNjMx6qPVILIOzjvzsUGauHvnCenkyqYRgLwZ+q5bv1Ly0eNKd0dmO6aSJd+qXU+qXlv1FplsspN6nxZ6TisokhObT/QH76jCTGYnMjFEZ/dA5M/8C/v1P4L/bkE4jm+h3I1IDIEiKCI0UekdMCcD6UkUviytMpqjM9LAtkjDubi43tuWTnbctlKJEX5XcK5EYAHORSVYzJNCRCk9q7OYZkRF5kQwfO59Dy8wez8M4V0Zqti2OT/uQU3N0Rp23WHFJzZc8ctGYmcjoqIit1gHiKKw9cS8HTWO3X4qgpiIrqUiMXQ9nHmo7u0wvJ78SlJmfBv0F1mJS8sW2icHyWi0rMMuAucwAeaHJ/RqzN7wUBf+yVmKM0OzvD9ghPO4jmXnCPR5x94eJyjgi8882LHrENE31twwAt31o6XTbIG7FlMyZqSOJkehM6PdlNz5SPfrqKhXdqR6Qbr6di8Jkq5M608T5HDypUUITBGbauScysdDEyzY4uYe1kiP96mzQRlJzqPbYfG7xvD3h8LQPicL11h/TTP9W8CSmJCJjRWYpCTgSGRvttFJjSTUYgPPm7PYlkpHLgUtFdXL3EY13XPIrQpn5qdF3Vbus9IsvNxZbDaWlx/vltJSsd47I2P0l3kPqV/DwfOy8DbZzuvB4aYDbLULvvPoxFC6/dQD+ALo2fmcSkfkNU+6MPH+4A/ayn88IUaI7TL0BNwhJqUZONFLw1uOZ56MNwpLE2P2rBUleAaweQ2l+YonpIDF1B9Q9brcnVHWHZntCVffYVNOo4FKtNLW3OmGPQ/Rcy4zXwktfxxbNMMzmfpQa2c8BO9RNiAodnnZDrdI2vFDEw4vSlEZfUiKzJDQdkI/G6CoeD+975zUm0P1bwVm/Rmg8wdHLU/tYOm/yq0KZ+Snxcmn0stRPx9x+tNR482t+ocl8LrlPyP1qzJx/7tcv0h3SndDgtP2E27vXUE5ZmRnyXR66kNwr5eAtQiXZKC/DdFcBv90Dt1aM9GMQmm/beDDIcJ7zsY+8fmWWSIlMrnXTd8ETGYnA1D0+DRGZZnvCponlxUZlgsTY6SQjWmRSOUShmmmPRyVCjZmvqh7V5/CaIwB020k4ciJzTMynBKYkOgMgjsZYgfGqc1Mfgsb7HqbuFWuEJlV1VPpjyDuOt578alBmfnq0uFihAWJZWXqdF5WRbUuT80rCy/rf0vtlaCm8kYnIdHEuyjRkQCgW22aDu+YYRENybdp4Pzcd8BcgVD0NZeEoMDDRmGEgSdyredm3POoQ6LBVTCm0nPSoxtY6FbqhaiksL5EYWecmDFflww2swmvqKyIzJPx+qntstq0rMl40RkvMDgc0OA1Rlmc0wweY62CwRj/s5XncT4jOhGNJBdz4+s/h9ceuArrbeCgLTS7SsiQxVmT06wHM+23xZEYv98h9f7xozNI+PKE5R1wYkSHlUGZ+CZaEJre9jsDYRN8lmYFZXvqrTL/eis1KoTERGXShcNY5JnGfLiE3BdtjLB62KTVCqvFfANz+CdRtWLUD8Fsz9AKsIy8iL1LNZCMzDXDafoKu8vKwClaKF5XwhCi+dDIopEoSXuorZg2R0MxFpqr7KeFXiYzIyUYJzdQn8sGZP0RJwCmxq9Bjh+dhny0e8TBEdHT3hXHC9Gm7wWl7wuv2dp4MnBMSKzOpaIz3+hEtLkvRGBtBXSJ3j0hFde2+10pLahkyy7zjkl8RysxPiy34rdAAcVQm1+rJRma02Mh2qRuZrLfLU5EZwasS896XPd8MQ8Hw2slv7DDVvevK49sdcGNlRImMcFMDv9UIPQF3Q5VSo14n0RgdnXFEBg3Q17HI2KoeLSRy9mtI5c7Yecnbke21WOkk3LOHJrBRGV21hElkqrobc2RSIrMfpEUiMHr+AY/R8/A+J4HxxrPa4zCIzGmseIz/U0zrr2aDdrvBcTv0RWOrzYSSSEtOZLSQz5J8vb5bUtPSgt/bzovietulJGetxKTuId6+ya8OZeanJic0sr4ULTLy3O5n6Zda6c1MH0dLlpax1M3tG6LeTe2hB6bE3ymdVqIyJzRoG2AribkJkdGn9JvkQUtCr43I2OiMypWRfJm2CseWYjOc9lxo5JFLANZVT7km2VqIRFqmwSmH58PwALPqprqH26uv/TeLXpM8ZZUj04+9/FZ1P+TGSHuueX6MFpkHPGI3TGOheU6KjK1m2kQiE1/veohiTQLc4HTfhCbb9W0cmZFr4UmMznvJyUsUifFyY2x/TUBaYrzvzNI9wOtp3GNJakqroXPblxyT/KpQZn56PKERbNTFIxXR0YKRCw3n6rtTNyx7zDMiMbrGys5HrZmqsejS86ftLbZ3L1MkxR6qUlN59IglRc9rmbHVTHdQ+TI6+df/enpyYvNl4lPNS8yUbxPnzshzOY+q7qNBJFdj346OyiREZlMFeRGhkYiMFRmRFmlmvxuW6eiMHeZA3qNe/oj7MT7XDHkytvWY6I10ZLipWtxuT3jZbtP9zeSEpnOm0f9bSXPrlMzY6qV10bx126fkojTaUpobQ4khcygzvwS2WkmwURBvnfd6K0RwlqfOIbWtrWKysmSTjlP7XaAD0A19qFReVVNos3Ko9vit+WMSDu0HlToNnSfRYRIXW8W0RSw5d/H60/YT2mggx/h92kiMFMA6yTdHSmL0OmCK6Mi8vEbm67qP82bqDujU/1Vp2efceazISFSmQqfG7varlrTI3Kt+g/TyuCVYh7rvUXU9qu4VVfApNJ9bNNUpkhc7ttMkMofxLJpti5f6G1DflEdmPJmJqpKsqHi5MZ1ZBqQlpqRK6FKUHust2xEyQZn5pUjlpAhWbHJRHW/71Ha587Cv86qYloRm4Xi6QAGA7iYkAVeqh9uhEkOiMy02eLlTSbwpXxCRaYZttLhYidG5M6oa6tsWaJuNSjNNJwHbdlg5dJRmqVly5URnRJaAUNUk1UxV3S23cLIfU0H10nhOUQ+/4YpMHeCdZvNTF4KHSGSi3p3bx0habuT/QQ8r0QO/HV9Q/fUrqmZ+nXWk6oQGB+yGZtwtNtvTEGFSeTOziCAWmlgD8yEI1iT2WqFJzS/hfc9Lt01RErVZ8zpCYigzvyxetMaLuOQSAdfWoXvYfeQSLnLom/ntvODQv4afgMPTHo/NAx7wgK9mLGqpxmg+n/C3/g/cHBGqhv5Uj3bYl16mIzNWaGr1/K/D/v4CfPsr8D9/2Ub9EB+wGytUpvGYplyaXJNtjVQ7aXJjNIV8kDqaj6qoalUFNsjHKwDUNaJhJPQUmedDFZNXvSQ9/Npoiq4msq2M9EPnulToi0RGplX3CjRl/fiMreLGIR3g57/kOr0bsdVHktj7rNbLfEpkvEhMSY5KjrcKy9r9UWDIeigzJEFOKuzNJtVXzdL+l7DRGP3c3sA7NV8D3Y0vMl8B3AMv//gNX7ctNne6l9h2VuXS/6XCf1b/wu2/EYTliEle9LwMUbBFqIbS0waTzEjOzG/A8TPweHePr/iiZOZ+jCscsA9VXtiP3bbpHovPQScEe+jkX9taqq+m1zXbFu2xAQC8btuQ9HO8HTvEHfH+hUwPv1Zk5DxqzIWm5P3ZxO4eFfr6Ewb1AqrgXzcdpipDmW+A0/Y2ys6R6kf5PKasHTVgwnETxmtKNcFOPQDkozHnDMwIpEXmR4pE6XeekPVQZn55ShJsl/5NLi0xpdEZKzSH4XXDwJMmGqNlBlvgafsFm+0JTdW6BaYUhO3nDR4+P6HpW+yeXnDbIo7OyHNPZur5MpGYA3ZjZcgB+1FonrHDIx6GAnQ39qYiBWxqPCZvXKZz8eRBBl4cn29bdF2F07EJmrAF3NZNQh1/9p+G4Qlsnoy0nrIRGZlfw1hRVIeH5MgAw3+YkZqXZmpVNo2HJV3zbcao2TP2sdAcm3KBkUgNgHTvvWuSe+HMI/Nc8z3k4dKRHULmUGYI8kIDrKtDz73uLXjRGVmuIzPya1ZFZ7TIbDHKDLa3+Lr9gupvcQsXAFFMoEUTqqCqAzafp2bBkdy06hRVwdjXQF9/Ql9X6KpqzLXQFVuPuE9097YfBz6cIjNTJct7kxIaeR8j23YahPJoXlD7ERW3asmMhA3E1WI6YdmLLsURmbi/nrZq4s+3Bvqh6klHak7bT6ph9jw6Y7N2Wmxw6hu8HjdnRGWWeu9NRWXgPPeqleAse29hOOc7T4khb4cyQwZSLZ48PLm5pLjIMbS8aF4Q+tq1QlNjis48A91+Kji+IkjMV0w5LDXwWt/hnwCqv0n1ijTXDjkqp6F6QY/zIz3KbqoWzeephU14vc3mmArVdpAR+WWvZSVEYJpo3nbcb/ctx9PTFHr07GnZvCWU7eHWLhupWvRVjb6ugONmWr71R6eevXxBZOKBJvxqJv1+7HhW3mN4EbqqQt33UaSmr4GqConY82qlODozpR+H6eFpF6rYVlcvaZHRHd95URkrMbl5IC82OS79Pc5BiSGXgzJDDGukRnjrDdBrZZU6jk5M3mGSmGdMERuZxxSV2WISGZlqoal/R//XqRjtjHSkRmLWvdFKfy1aNGyEILSECXt6xm42ypAsF5FqVURG5ruMwFwqYlNcnVMBm+0JVVeh70I/NNJ0u6rz+1gWmbjHXS8iM68anK5NnD8zLa/Qo6+C3HVVhVrydLp+Ji5xdGaqepqer6xicqMyOYER0QHWVSnpZXa5t/57QYEh7wNlhiTItWK61P5S25WO+C0io5OCpa30M4DhF7PIjFQ16V5ah+lL9xv+2VXof6+GnIkgMiIzdiwgb7BDaT2Uy2UZ83AiSWnGTvtkXouPFMyyvd6nnZ+Wrf9q6zGapBO++foera5iCisi6kTV0ux4CyKjq5XsUA4e82jVvG2T9OCr++jRYtMacdECo/NmxufnVDEBmETGkxldxeRVJ6HgOcxyb933gPJCvg+UGbKC731jsuKihUY/dHRGs0OUCPwVcSd3dby71+4O/zpucPp9g9PdBgeVu2IjMF5UZuqHZCrd4+qPOGLTDsm8cRwiFhgv/0OLSupYer0t/JdaNAFICg0w5M0M5yFa0FXVVO1kz8HpkyZO9M2JjBmpuhDdiFtfP7keWmzCvsM0H5nZRGJzwB6n42ZdFVMHxFGZXI6M1xkenOd6mV1u1+WgeJDrhTJDPiC5SIwXHdJJwTYReAc83U6tihyJGR9HAMdbPD39Lxx+3+PxywMemwd8xRfscUA1SIwewVnEZoM2+448AUlJiq0q0a9P5ch40nQOWsiWhMdSi9SY41eVv5959GUekbGJwJ7Y2HwZey3nAlhD92qs92fFpR0TsCeJ0evatVVMAGKR8WQm1RQb8AVmTRUShYX8nFBmyJWiq5Rq81yoMUZnpKrJbgIYmQHwBLwe7/DH0x6HL3s8fHnEpprERWSmQj+M3zMtL8UW+FZOlp6n5GWp+ulcSsRminLYROPy24wdsTtEh6pxqqNaFXqcxmmjKpSCBB2wi/brfT72fUkfP6GKcTdrNv84rDtgh+d2j5en3bz5v+0YT+Y7YIq4eDJjRcYm/wLpCEzuf48CQ35+KDPkCshFZ8REgPmYTlJY7IBuEBpv126EBsDTDV6efsO/nnZRnyiNzFdaZtrVkYwlSpJ5S6RIRzP0CNHTspKxnabehG21mH0+HrtXYuUOf7CZOsmr/HPbYIMTTmixwR7PaLEZ4yPP2GOHwzBC025M1H50xm9qnMiZ975FWL7iy9iZoZ5+xRf8D77gn/3v+OMfX4B/3IbqS/uwcjNGZXT1kZUXLxKjWdsyac12hFw3lBnyQcn1fSNRmG7YRp7LjXuHqUXTYXheA0+my/2UxNzD9E9zi9ftLV5r4GULHGtMXfGbjt+EKpEEm1oebZOolsmhxSFa3lXJ/JTwPE6uTbUWyiUzR2LTq+791VSPtq0HqpQWT2OLIjPdVEFmNkOWkm1RJvlMMhV5EaGRZQ3yTcblfWqZ0dNoWf8F//p//wr8v1vgH5geX+FHaWZRGR11SYlMTmxKc2IoMuTXgTJDrgQdnZHnIjLyXJDths7zxul+is7ogE5KZu4Rtr9HXEVVA6hvgO0tXutBcrwaLo104T8+NwWQkpxPjvB4EhQGfCz7Cqd72Y3l5oS4512bLOw1Pdci03fVTGCkQz0AeBWp6Sqg7vGCzew96/fabDc4bU+j1FQqGVsnYB+wH/r7CQM/Npg6NxQBKkE6MdTyooes/PrnFzx9ffBFRh6e0MySfrXE2BZLOSgyhHhQZsgVYYVG94nj3eRFZP6tlg9C40VkdI6DSIw8tMjMxEY9hNm8iQpBja5cx89fTbPxaBkwilFxcVV3eBlGpp4iH36HdeGwIjPSVmnKN7FNzz2R0X3N9F0VC0xXA93NdA2G9/OqTvd1OGcAeDlucLs9odlu8Fz32DQnHLCbJWCL2DxL78xo8YiHqAm9xcvl0UNMRI/+AY9fH/Dy9QH4epMWGXlEIgPM82S80a/hLFub5EuJIb8mlBnygfGqmqzQyHYeUtUkkRlhFwYFdBJ/xyjMvZlPycuSzOSW1Zn57NSK0QL1LTBEkF6H6jEtN802FPQiNv0gNhKV0bklOhoTnvsiM0qMCAwQJEZL5Hj+N5gjonOLl22Dl3rKW2q2LZ6Hc91UbZSM/YgHt0+gVD6TzUt6Vsm+Y+LvH/c4Pu2Br9tJVrTIpKqZRpE5IPwPytSrWgJ8uQHKojGUGPJrQ5khH5yU0JRik4VfAPyGMSn4K8K34IipY71o/CbkIzFLQgJnWe61uf1585rUtzmKKN0oufmG17qb8mqiaE2FqpqiMiI1XjQGQF5krMToKj6Nt6wGsL0Zzlfyll4AI2PVELXRCdm2xZkm1dJLBOaAfSwxT5hk5StikfnqPGYi43WQ5/Uj48lN7gIBFBlCKDPkKsglA6dKRZ038+I89giJwUZqtmqakhmgTD5S0zWPkmNpUsu2CAVsVE025P10NV5VtGazbaMqKF3up6qVAMQic2zmEnPEeTIj5z0+dKQJeKm/Adt2du6bJm5GX9Lc/dQ3oTrpaeifSEuMFZqv8GXmCZjnyPwbsdAckJcXfTHY7JqQJSgz5ErwmmQDcXNsy7PaRl4jj6HJNm4RSc2TOoYWm9KISekyL9pTEgHypEbjfaNFZGT/er7DKDWog9Qcuwq329MYqdlsT6PQ5KqVAMxFRguMlZlUeW1zuRdF8Aaot3itgWN9N1zLOHojraZ0wrRuLj7m9RybkBMj8pJ6fEUsL3oeAPCIuGrJy5dJ5cXYSI2FAkOIhTJDrgid8KuXaaHRz+XfWwqNPaYwf41JZvS8en4c5p+cnI6lyEtu3pOk1PyS7Njz8eZ1RCaSGDudpOYFCEMTbFvguBmFpig/xopMaoqCael1nT1U9AZDazO9X+9YwLzju6fEsq/wozYAQhTGRmK8jvJKx13SywghHpQZcoVYYUk9B+IC4YAgNMIz4tJ8h/m+JHIDTF+XG78g1JvkojOz6p7MfGmEBpl5W3VWL01vgO0Q6dhugLofIzWrcmKWZKZEaFLX0bvGqWsj80uRIJsMbgXGExz9ABBHYkRYbCQGiecpKDKELEGZIT8ZWmg8DphkZeesl1JPS42WI/2VscsWJMdGZrTU6LyQNTJjT8mb94SlSGowRGqkRVGXl5e1IlMiNPp9nCM1etlSVMbKjJ3PyU0HTOIiQpNK9hXJ8SiRG0KIhTJDrpxUNCYnNBop5VJTqZKSfWuBeUZc5VWrbZTgyClJ+WTFxSa4LvVjsxSZsctyArMkM1G05nZZYjyZWapmWlPVtDRNzWtyERrb55AVG09qAIT/Na9qScuLrlqS18B5TghZC2WG/ER4QqPXpba3EiOSUqup7rMGZt+e6OSe38aFfmkUJiU3+hBeAX6uyHhi44kLkBaZnNAAvsAsyUxq3hMYe52Wqpo8mck9AMxFRif52vyYDrHQeCLDXBlC1kKZIVdKSlx0kvBSHsKtmdf78cTGto5CYrkjL7PlSpS6m/hUSwTGew4zL9OUwKyRGJ04vDYykxKaJZGxPmrvViUyp6cpkdHP7fna844kJtf8urRzPCAtMoSQUigz5IrxEn1rs84rHLwkX13YeKNve9VJKFjuRWl0qym7PCM3ej4VhfCmS1GZnLzY6SWrmTx5sfMa726VExq7bCkqg8S5WvmaSYzXWsnmyMDMCzmRYVSGkFIoM+TK8fqf8YSmxvQr2S6zIiTVA1pEvKiMfg3M+iWxqc3UkZpRboBpPCPn0Etis0ZkcmKjL89bojJeZCZXzeS959zyXDQnFZER9Lkd9YpviKuNrMR46zpnCswlxzsRigwha6DMkJ+AVId6pa/TYmNfb6uRnhGTkhk9b8UmJzVAXnDU6zogakFl0ZGZtTKTWubJC8zzJYnJRWZyIlPCUvRmSWZGRF5EPGzuy3Ni/mBel+rdN9dqiSJDyFooM+QnQRcAS2KTq57yXiPrrMh4QyyUVkGlpAZIykv0WjivQfxcojlPmMacWpKapSjNW6uZvBZNM76plaUF+/C+vX2Ovf4uRT+8yEkqwpKSnFQkxu6/5HwIIaVQZshPiNdTsJWWXMGxtK3XIZ+QkiK7PJWfkxKVJYHJ5ejsANxMMrHFJBR6XsRFz6+RGSAWlaXqJQBBXPQ1sULxnqTyV0qqh6zspNYLFBlC3hPKDPmJsdVPgB+JOWdbTUqaSltW2WPWZv7FmYfZzvaHo7fpEISmxig1nVolUlMjLzWl1UwpoRkvga7CQWb+PQv4nEzkJEavz7VSyomMhSJDyFuhzJCfHE9SgFga9LZwtk9VO9nXCTbaYqvAvMQNL1NVXus1G0fiubS8siL1gmj8KS0etZrKw5MaKzMwz1NCE71dr0pGLwfmMuCxFBVb+1pPZnLzS82t7XHYjwwh7wllhvwC5IQGyMtJ7nUWWy21JiIj+9UC4yUo5xKWU3KzU6/T408NeTW6mslGYvRyLTPAXF70MrclkM0zAeZCAyArM6XXM3VrS70+JzOy3lu2NO/t21tOCHkLlBnyi5ASGiAuYJaiLlixn1zExnttSlh0pMUTsJzM6H0bkRkH0Ryqn7SI1IlHSmbs2wcwb86sq2eezYtKk2Tfg1wisExLztM755TIEEIuCWWG/EKkqpE0ObGx+/FIJQd7cpM7Bys2dh+6d+GUzMgyW5ckMrPHXG6GfaQ67ptVG2m8ZN5LtvZ5D7zPJHU+3rKl5/Z1ueMSQs6FMkN+QZaqkYRcYVoqOrmWT7J9aliGnMh4haZ3LE9mPLlJddo3LIvkRkuLnIOdzwlMqhrGi4i8J6VVTqllevmaaAxFhpBLQ5khvzi5lkk5lqIGuSbgueomKzeyr1QB6VU3PZvldvgEbwrMJcZblxIYPV0SmKXXwiy7NEufXS6qkhOVkioligwh7wFlhpCItYVNSf5MyTFL+8SxIrSmXxvpwTgnMt587jglSbFeq6U10Y0c71EV5R27JMJCgSHkR0GZIeRNlBZU5w63kHttri8bKx+pZtu3Zp1+rfd8bf6IbQVUko9il7+Ft8hO7hxy+6W8EPK9ocwQ8l3ItaYC/FZK+rVwXm9zbPQ2XhVVKgk51YIqlYhcklOy1BKotLpG871aAy3JCEWGkI8GZYaQ78aa1lSlUpOqkrJJxWsL2ZTI2HMB/IhKSTTmnJyTS3LONbnk/gghl4IyQ8h3ZylKA5RJDeBHYuw2JVh5AeYRGntu9jipiE1OYkrzTizfSxw+2vkQQjwoM4T8EEqiNEBearz9eHkzXrWR3n9qKAX9WltYl7T4Ka2Wsq9PHeN7sTYqRJEh5EdDmSHkh7JWaoSSIRiW+snxhMJKjN6PbprtHdce86P1wXLJqisKDCEfCcoMIR8CT0ZypFou2X3J/jwJyXXWp4/hVV0ticglBOZ7JfyWQoEh5KNCmSHkw5HqaC9FadQmN5q3t7+S/Js1veh660r29b2htBBybVBmCLkKUgVs6ajeSy2avNyZVLRobdPl0k7oUlAuCCF5KDOEXDWlklMiD7mqrrc0S157bEIIWQdlhpCfkqUcnFzOjX39ObBPFkLI9+PTjz4BQsh7U1ItdKl8lZJ9UWQIIZeFkRlCfgnWdNQnlN4emP9CCPmxUGYI+WUo7dNG8OTm3AgOJYYQ8n5QZgj55VgrNcI5IkOJIYS8P5QZQn5Z1nbUd85+CSHk/aHMEEKQFxBPdCgshJCPA2WGELIAxYUQ8rFh02xCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDX/f8hC6a/UAuyuAAAAAElFTkSuQmCC" id="image380255bca9" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p4e4cb95f1a)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p4e4cb95f1a)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p4e4cb95f1a)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="mf6a6a33723" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mf6a6a33723" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#mf6a6a33723" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#mf6a6a33723" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#mf6a6a33723" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#mf6a6a33723" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#mf6a6a33723" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#mf6a6a33723" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#mf6a6a33723" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#mf6a6a33723" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m2ec532ddba" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m2ec532ddba" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845938 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m2ec532ddba" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845938 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m2ec532ddba" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845938 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m2ec532ddba" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845938 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m2ec532ddba" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m2ec532ddba" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m2ec532ddba" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m2ec532ddba" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p4e4cb95f1a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m2ec532ddba" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Mid-Distance Cell (Neuron 21) -->
    <g transform="translate(75.364719 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-64" d="M 2919 2988 
L 2919 4863 
L 4044 4863 
L 4044 0 
L 2919 0 
L 2919 506 
Q 2688 197 2409 53 
Q 2131 -91 1766 -91 
Q 1119 -91 703 423 
Q 288 938 288 1747 
Q 288 2556 703 3070 
Q 1119 3584 1766 3584 
Q 2128 3584 2408 3439 
Q 2688 3294 2919 2988 
z
M 2181 722 
Q 2541 722 2730 984 
Q 2919 1247 2919 1747 
Q 2919 2247 2730 2509 
Q 2541 2772 2181 2772 
Q 1825 2772 1636 2509 
Q 1447 2247 1447 1747 
Q 1447 1247 1636 984 
Q 1825 722 2181 722 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-4d"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="99.511719"/>
     <use xlink:href="#DejaVuSans-Bold-64" x="133.789062"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="205.371094"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="246.875"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="329.882812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="364.160156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="423.681641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.484375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="538.964844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="610.15625"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="669.433594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="737.255859"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="772.070312"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="845.458984"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="913.28125"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="947.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="981.835938"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1016.650391"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1062.353516"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1146.044922"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1213.867188"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1285.058594"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1334.375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1403.076172"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1474.267578"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1509.082031"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="1578.662109"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1648.242188"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m063a2bdb55" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p4e4cb95f1a)">
     <use xlink:href="#m063a2bdb55" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m063a2bdb55" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 446.411015 314.62525 
L 818.449288 314.62525 
L 818.449288 44.84925 
L 446.411015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="me4d70f7d08" d="M 451.057232 -37.642188 
L 451.057232 -37.642188 
L 460.349665 -37.642188 
L 469.642098 -59.695524 
L 478.934531 -54.606687 
L 488.226965 -58.386944 
L 497.519398 -50.728224 
L 506.811831 -128.199594 
L 516.104264 -100.088685 
L 525.396697 -148.646226 
L 534.689131 -199.460493 
L 543.981564 -86.128044 
L 553.273997 -147.158899 
L 562.56643 -137.214266 
L 571.858863 -178.576185 
L 581.151296 -132.213659 
L 590.44373 -209.292197 
L 599.736163 -162.52604 
L 609.028596 -251.852235 
L 618.321029 -294.571711 
L 627.613462 -133.258425 
L 636.905896 -202.757787 
L 646.198329 -265.463391 
L 655.490762 -123.486606 
L 664.783195 -162.845638 
L 674.075628 -236.375676 
L 683.368061 -110.903852 
L 692.660495 -129.704737 
L 701.952928 -243.038354 
L 711.245361 -272.171203 
L 720.537794 -267.937601 
L 720.537794 -37.642188 
L 720.537794 -37.642188 
L 711.245361 -37.642188 
L 701.952928 -37.642188 
L 692.660495 -37.642188 
L 683.368061 -37.642188 
L 674.075628 -37.642188 
L 664.783195 -37.642188 
L 655.490762 -37.642188 
L 646.198329 -37.642188 
L 636.905896 -37.642188 
L 627.613462 -37.642188 
L 618.321029 -37.642188 
L 609.028596 -37.642188 
L 599.736163 -37.642188 
L 590.44373 -37.642188 
L 581.151296 -37.642188 
L 571.858863 -37.642188 
L 562.56643 -37.642188 
L 553.273997 -37.642188 
L 543.981564 -37.642188 
L 534.689131 -37.642188 
L 525.396697 -37.642188 
L 516.104264 -37.642188 
L 506.811831 -37.642188 
L 497.519398 -37.642188 
L 488.226965 -37.642188 
L 478.934531 -37.642188 
L 469.642098 -37.642188 
L 460.349665 -37.642188 
L 451.057232 -37.642188 
z
" style="stroke: #1f78b4; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#p593254fc4b)">
     <use xlink:href="#me4d70f7d08" x="0" y="352.267438" style="fill: #1f78b4; fill-opacity: 0.3; stroke: #1f78b4; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 446.411015 314.62525 
L 446.411015 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#mf6a6a33723" x="446.411015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(443.54789 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 492.915799 314.62525 
L 492.915799 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#mf6a6a33723" x="492.915799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(490.052674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 539.420584 314.62525 
L 539.420584 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#mf6a6a33723" x="539.420584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(536.557459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 585.925368 314.62525 
L 585.925368 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#mf6a6a33723" x="585.925368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(583.062243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 632.430152 314.62525 
L 632.430152 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#mf6a6a33723" x="632.430152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(629.567027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 678.934936 314.62525 
L 678.934936 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#mf6a6a33723" x="678.934936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(676.071811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 725.43972 314.62525 
L 725.43972 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#mf6a6a33723" x="725.43972" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(722.576595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 771.944504 314.62525 
L 771.944504 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#mf6a6a33723" x="771.944504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(769.081379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 818.449288 314.62525 
L 818.449288 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#mf6a6a33723" x="818.449288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(815.586163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(555.087261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 446.411015 314.62525 
L 818.449288 314.62525 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m2ec532ddba" x="446.411015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.00 -->
      <g transform="translate(419.371953 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 446.411015 281.308651 
L 818.449288 281.308651 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m2ec532ddba" x="446.411015" y="281.308651" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.25 -->
      <g transform="translate(419.371953 284.727948) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 446.411015 247.992051 
L 818.449288 247.992051 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m2ec532ddba" x="446.411015" y="247.992051" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.50 -->
      <g transform="translate(419.371953 251.411348) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 446.411015 214.675452 
L 818.449288 214.675452 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m2ec532ddba" x="446.411015" y="214.675452" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.75 -->
      <g transform="translate(419.371953 218.094749) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-37" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 446.411015 181.358853 
L 818.449288 181.358853 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m2ec532ddba" x="446.411015" y="181.358853" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 1.00 -->
      <g transform="translate(419.371953 184.77815) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 446.411015 148.042254 
L 818.449288 148.042254 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m2ec532ddba" x="446.411015" y="148.042254" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 1.25 -->
      <g transform="translate(419.371953 151.46155) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 446.411015 114.725654 
L 818.449288 114.725654 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m2ec532ddba" x="446.411015" y="114.725654" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 1.50 -->
      <g transform="translate(419.371953 118.144951) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 446.411015 81.409055 
L 818.449288 81.409055 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m2ec532ddba" x="446.411015" y="81.409055" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 1.75 -->
      <g transform="translate(419.371953 84.828352) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-37" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_71">
      <path d="M 446.411015 48.092456 
L 818.449288 48.092456 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_72">
      <g>
       <use xlink:href="#m2ec532ddba" x="446.411015" y="48.092456" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_44">
      <!-- 2.00 -->
      <g transform="translate(419.371953 51.511753) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_45">
     <!-- Neural Activation -->
     <g transform="translate(413.084297 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_73">
    <path d="M 451.057232 314.62525 
L 460.349665 314.62525 
L 469.642098 292.571913 
L 478.934531 297.66075 
L 488.226965 293.880494 
L 497.519398 301.539214 
L 506.811831 224.067844 
L 516.104264 252.178752 
L 525.396697 203.621212 
L 534.689131 152.806945 
L 543.981564 266.139393 
L 553.273997 205.108538 
L 562.56643 215.053172 
L 571.858863 173.691253 
L 581.151296 220.053779 
L 590.44373 142.975241 
L 599.736163 189.741398 
L 609.028596 100.415202 
L 618.321029 57.695726 
L 627.613462 219.009013 
L 636.905896 149.50965 
L 646.198329 86.804047 
L 655.490762 228.780832 
L 664.783195 189.421799 
L 674.075628 115.891762 
L 683.368061 241.363585 
L 692.660495 222.562701 
L 701.952928 109.229084 
L 711.245361 80.096235 
L 720.537794 84.329837 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_74">
    <path d="M 618.321029 314.62525 
L 618.321029 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #1f78b4; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_75">
    <path d="M 585.925368 314.62525 
L 585.925368 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_76">
    <path d="M 678.934936 314.62525 
L 678.934936 44.84925 
" clip-path="url(#p593254fc4b)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 446.411015 314.62525 
L 446.411015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 446.411015 314.62525 
L 818.449288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_46">
    <g id="patch_15">
     <path d="M 705.406124 114.0718 
L 799.847374 114.0718 
Q 803.047374 114.0718 803.047374 110.8718 
L 803.047374 58.33805 
Q 803.047374 55.13805 799.847374 55.13805 
L 705.406124 55.13805 
Q 702.206124 55.13805 702.206124 58.33805 
L 702.206124 110.8718 
Q 702.206124 114.0718 705.406124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 3.7m -->
    <g transform="translate(717.581124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-33" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-37" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 1.928 -->
    <g transform="translate(712.573624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-31" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-39" x="900.056641"/>
     <use xlink:href="#DejaVuSans-32" x="963.679688"/>
     <use xlink:href="#DejaVuSans-38" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.305 -->
    <g transform="translate(739.677374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-33" x="561.255859"/>
     <use xlink:href="#DejaVuSans-30" x="624.878906"/>
     <use xlink:href="#DejaVuSans-35" x="688.501953"/>
    </g>
    <!-- Peak Significance: 2.55 -->
    <g transform="translate(706.859874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-32" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-35" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-35" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.273 -->
    <g transform="translate(705.406124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-32" x="989.607422"/>
     <use xlink:href="#DejaVuSans-37" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-33" x="1116.853516"/>
    </g>
    <!-- Peak Width: 4.0m -->
    <g transform="translate(729.323624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-34" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-30" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_47">
    <!-- Distance Tuning Curve -->
    <g transform="translate(556.616402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 3.7m, Sparsity: 0.305 -->
    <g transform="translate(538.691402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-37" d="M 428 4666 
L 3944 4666 
L 3944 3988 
L 2125 0 
L 953 0 
L 2675 3781 
L 428 3781 
L 428 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-37" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="mcf5e819e7d" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p593254fc4b)">
     <use xlink:href="#mcf5e819e7d" x="618.321029" y="57.695726" style="fill: #1f78b4; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 452.011015 86.47675 
L 568.918515 86.47675 
Q 570.518515 86.47675 570.518515 84.87675 
L 570.518515 50.44925 
Q 570.518515 48.84925 568.918515 48.84925 
L 452.011015 48.84925 
Q 450.411015 48.84925 450.411015 50.44925 
L 450.411015 84.87675 
Q 450.411015 86.47675 452.011015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_77">
     <path d="M 453.611015 55.328 
L 461.611015 55.328 
L 469.611015 55.328 
" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_48">
     <!-- Tuning Curve -->
     <g transform="translate(476.011015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_78">
     <path d="M 453.611015 67.0705 
L 461.611015 67.0705 
L 469.611015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_49">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(476.011015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_79">
     <path d="M 453.611015 78.813 
L 461.611015 78.813 
L 469.611015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_50">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(476.011015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.49182 287.64765 
L 341.28286 287.64765 
L 341.28286 71.82685 
L 330.49182 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABYAAAHBCAYAAABt+EsYAAACDUlEQVR4nO2bS25CQQwEDUzunXsHQhYz9MsFelFS1QGskrv9+AhuM9/vKbBmVmPu3CtTZ2bNfFUGN43dcQa7io11C9YtIOvGM+60wlUE6xasW7BuwR0HX/4Dsm48Y5/HG96B3GbelS9CasYOBg9ec+sMLho/WoM7D7fmKjoPN40vrFsghscztm4HYng8Y+t2IIbHM7ZuB2J4PGPrdiCGxzO2bgdieDzjUt2KxqUeE8PT+MA7EJ/HQeNQM3YVQePg0y0Qw9O4PZi3Co2DT7dADE/ja3Dl1zzVVbwqgzUO6/54dgavL9oqHrjwiMatuvFWcccZj8abpnHrQICr0Hhj3YLhBY3/D+70uLcKD+QDz9i6BV54PGPrFnjh8YyRq/BANtYtWLdg3fqDeavwQELtQJDh8Yyt2xlMDE/jjQcSPJCANLZuZzAxPJ6xdTuDieFpvOEdiF8rBI2DdQvI8DTOYN9XbHjhFY1frbo9cXV7/VYGF8PrrBhovG6dGjf/XYELT+NrsHU7aBxqrSCuQuMDr268HRte4IXHMza8oHGwbsHwAu8VhLgKnrEn3R7MW4V1C9YtGF7gGRNXYd0OPGPDCxoHPyoEjQOvbrwdG17ghcczNrygcbBuYf3gwiv9jq6549a7TeAqcMYl4eaOaYMNL7jja7Cr+GDdgnUL1i3wjA0veCDBugXrFng75q3iD9EGue+xdPB4AAAAAElFTkSuQmCC" id="image74cafaf5cd" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="10.56" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_19">
     <g id="line2d_80">
      <defs>
       <path id="m31a0829b19" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m31a0829b19" x="341.28286" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.00 -->
      <g transform="translate(348.28286 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_81">
      <g>
       <use xlink:href="#m31a0829b19" x="341.28286" y="262.182343" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.25 -->
      <g transform="translate(348.28286 265.60164) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_82">
      <g>
       <use xlink:href="#m31a0829b19" x="341.28286" y="236.717036" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.50 -->
      <g transform="translate(348.28286 240.136332) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_83">
      <g>
       <use xlink:href="#m31a0829b19" x="341.28286" y="211.251728" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.75 -->
      <g transform="translate(348.28286 214.671025) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-37" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_84">
      <g>
       <use xlink:href="#m31a0829b19" x="341.28286" y="185.786421" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 1.00 -->
      <g transform="translate(348.28286 189.205718) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_85">
      <g>
       <use xlink:href="#m31a0829b19" x="341.28286" y="160.321114" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 1.25 -->
      <g transform="translate(348.28286 163.740411) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_86">
      <g>
       <use xlink:href="#m31a0829b19" x="341.28286" y="134.855807" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_57">
      <!-- 1.50 -->
      <g transform="translate(348.28286 138.275104) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_26">
     <g id="line2d_87">
      <g>
       <use xlink:href="#m31a0829b19" x="341.28286" y="109.390499" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_58">
      <!-- 1.75 -->
      <g transform="translate(348.28286 112.809796) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-37" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_27">
     <g id="line2d_88">
      <g>
       <use xlink:href="#m31a0829b19" x="341.28286" y="83.925192" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_59">
      <!-- 2.00 -->
      <g transform="translate(348.28286 87.344489) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_60">
     <!-- Neural Activation -->
     <g transform="translate(374.963641 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.49182 287.64765 
L 335.88734 287.64765 
L 341.28286 287.64765 
L 341.28286 71.82685 
L 335.88734 71.82685 
L 330.49182 71.82685 
L 330.49182 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p4e4cb95f1a">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="p593254fc4b">
   <rect x="446.411015" y="44.84925" width="372.038273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
