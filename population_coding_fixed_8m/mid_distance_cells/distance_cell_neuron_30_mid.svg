<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:40.627733</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p0223db9949)">
    <image xlink:href="data:image/png;base64,
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" id="image0ab5be7b84" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p0223db9949)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p0223db9949)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p0223db9949)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m105f677b24" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m105f677b24" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m105f677b24" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m105f677b24" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m105f677b24" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m105f677b24" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m105f677b24" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m105f677b24" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m105f677b24" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m105f677b24" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="mcb23a512ba" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mcb23a512ba" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#mcb23a512ba" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#mcb23a512ba" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#mcb23a512ba" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#mcb23a512ba" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#mcb23a512ba" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#mcb23a512ba" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#mcb23a512ba" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p0223db9949)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#mcb23a512ba" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Mid-Distance Cell (Neuron 30) -->
    <g transform="translate(75.364719 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-64" d="M 2919 2988 
L 2919 4863 
L 4044 4863 
L 4044 0 
L 2919 0 
L 2919 506 
Q 2688 197 2409 53 
Q 2131 -91 1766 -91 
Q 1119 -91 703 423 
Q 288 938 288 1747 
Q 288 2556 703 3070 
Q 1119 3584 1766 3584 
Q 2128 3584 2408 3439 
Q 2688 3294 2919 2988 
z
M 2181 722 
Q 2541 722 2730 984 
Q 2919 1247 2919 1747 
Q 2919 2247 2730 2509 
Q 2541 2772 2181 2772 
Q 1825 2772 1636 2509 
Q 1447 2247 1447 1747 
Q 1447 1247 1636 984 
Q 1825 722 2181 722 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-4d"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="99.511719"/>
     <use xlink:href="#DejaVuSans-Bold-64" x="133.789062"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="205.371094"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="246.875"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="329.882812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="364.160156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="423.681641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.484375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="538.964844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="610.15625"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="669.433594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="737.255859"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="772.070312"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="845.458984"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="913.28125"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="947.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="981.835938"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1016.650391"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1062.353516"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1146.044922"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1213.867188"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1285.058594"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1334.375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1403.076172"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1474.267578"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1509.082031"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1578.662109"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1648.242188"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m051591e4cb" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p0223db9949)">
     <use xlink:href="#m051591e4cb" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m051591e4cb" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m4d55c2d16c" d="M 440.975667 -37.642188 
L 440.975667 -37.642188 
L 450.408971 -42.609725 
L 459.842275 -47.938076 
L 469.275579 -55.321892 
L 478.708883 -79.779609 
L 488.142187 -79.678709 
L 497.575491 -71.409588 
L 507.008795 -73.404695 
L 516.442099 -80.361843 
L 525.875403 -64.777234 
L 535.308707 -110.462536 
L 544.742011 -139.121814 
L 554.175315 -162.618522 
L 563.608619 -158.045796 
L 573.041923 -170.269527 
L 582.475227 -129.542534 
L 591.908531 -186.538494 
L 601.341835 -157.44904 
L 610.775139 -186.930257 
L 620.208443 -294.571711 
L 629.641747 -274.87543 
L 639.075051 -203.792376 
L 648.508355 -273.597625 
L 657.941658 -256.903591 
L 667.374962 -240.216766 
L 676.808266 -277.399665 
L 686.24157 -266.232356 
L 695.674874 -262.02033 
L 705.108178 -210.923195 
L 714.541482 -144.166808 
L 714.541482 -37.642188 
L 714.541482 -37.642188 
L 705.108178 -37.642188 
L 695.674874 -37.642188 
L 686.24157 -37.642188 
L 676.808266 -37.642188 
L 667.374962 -37.642188 
L 657.941658 -37.642188 
L 648.508355 -37.642188 
L 639.075051 -37.642188 
L 629.641747 -37.642188 
L 620.208443 -37.642188 
L 610.775139 -37.642188 
L 601.341835 -37.642188 
L 591.908531 -37.642188 
L 582.475227 -37.642188 
L 573.041923 -37.642188 
L 563.608619 -37.642188 
L 554.175315 -37.642188 
L 544.742011 -37.642188 
L 535.308707 -37.642188 
L 525.875403 -37.642188 
L 516.442099 -37.642188 
L 507.008795 -37.642188 
L 497.575491 -37.642188 
L 488.142187 -37.642188 
L 478.708883 -37.642188 
L 469.275579 -37.642188 
L 459.842275 -37.642188 
L 450.408971 -37.642188 
L 440.975667 -37.642188 
z
" style="stroke: #1f78b4; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#p0e965b8a21)">
     <use xlink:href="#m4d55c2d16c" x="0" y="352.267438" style="fill: #1f78b4; fill-opacity: 0.3; stroke: #1f78b4; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m105f677b24" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m105f677b24" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m105f677b24" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m105f677b24" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m105f677b24" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m105f677b24" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m105f677b24" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m105f677b24" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m105f677b24" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#mcb23a512ba" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 269.327641 
L 813.937288 269.327641 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#mcb23a512ba" x="436.259015" y="269.327641" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.1 -->
      <g transform="translate(414.946203 272.746938) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 224.030033 
L 813.937288 224.030033 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#mcb23a512ba" x="436.259015" y="224.030033" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.2 -->
      <g transform="translate(414.946203 227.449329) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 178.732424 
L 813.937288 178.732424 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#mcb23a512ba" x="436.259015" y="178.732424" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.3 -->
      <g transform="translate(414.946203 182.151721) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 133.434815 
L 813.937288 133.434815 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#mcb23a512ba" x="436.259015" y="133.434815" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.4 -->
      <g transform="translate(414.946203 136.854112) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 88.137206 
L 813.937288 88.137206 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#mcb23a512ba" x="436.259015" y="88.137206" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.5 -->
      <g transform="translate(414.946203 91.556503) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_42">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_67">
    <path d="M 440.975667 314.62525 
L 450.408971 309.657713 
L 459.842275 304.329362 
L 469.275579 296.945546 
L 478.708883 272.487828 
L 488.142187 272.588729 
L 497.575491 280.857849 
L 507.008795 278.862743 
L 516.442099 271.905595 
L 525.875403 287.490203 
L 535.308707 241.804902 
L 544.742011 213.145623 
L 554.175315 189.648915 
L 563.608619 194.221641 
L 573.041923 181.997911 
L 582.475227 222.724903 
L 591.908531 165.728943 
L 601.341835 194.818397 
L 610.775139 165.337181 
L 620.208443 57.695726 
L 629.641747 77.392008 
L 639.075051 148.475061 
L 648.508355 78.669812 
L 657.941658 95.363846 
L 667.374962 112.050672 
L 676.808266 74.867773 
L 686.24157 86.035081 
L 695.674874 90.247108 
L 705.108178 141.344242 
L 714.541482 208.100629 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_68">
    <path d="M 620.208443 314.62525 
L 620.208443 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #1f78b4; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_69">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_70">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#p0e965b8a21)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_43">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 3.9m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-33" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-39" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.567 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-35" x="900.056641"/>
     <use xlink:href="#DejaVuSans-36" x="963.679688"/>
     <use xlink:href="#DejaVuSans-37" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.328 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-33" x="561.255859"/>
     <use xlink:href="#DejaVuSans-32" x="624.878906"/>
     <use xlink:href="#DejaVuSans-38" x="688.501953"/>
    </g>
    <!-- Peak Significance: 2.62 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-32" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-36" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-32" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.344 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-33" x="989.607422"/>
     <use xlink:href="#DejaVuSans-34" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-34" x="1116.853516"/>
    </g>
    <!-- Peak Width: 2.8m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-32" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-38" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_44">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 3.9m, Sparsity: 0.328 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-38" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m0d327b4b70" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p0e965b8a21)">
     <use xlink:href="#m0d327b4b70" x="620.208443" y="57.695726" style="fill: #1f78b4; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 691.429788 86.47675 
L 808.337288 86.47675 
Q 809.937288 86.47675 809.937288 84.87675 
L 809.937288 50.44925 
Q 809.937288 48.84925 808.337288 48.84925 
L 691.429788 48.84925 
Q 689.829788 48.84925 689.829788 50.44925 
L 689.829788 84.87675 
Q 689.829788 86.47675 691.429788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_71">
     <path d="M 693.029788 55.328 
L 701.029788 55.328 
L 709.029788 55.328 
" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_45">
     <!-- Tuning Curve -->
     <g transform="translate(715.429788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_72">
     <path d="M 693.029788 67.0705 
L 701.029788 67.0705 
L 709.029788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_46">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(715.429788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_73">
     <path d="M 693.029788 78.813 
L 701.029788 78.813 
L 709.029788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(715.429788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image7b6b19f635" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_16">
     <g id="line2d_74">
      <defs>
       <path id="m2dedd86f22" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m2dedd86f22" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_75">
      <g>
       <use xlink:href="#m2dedd86f22" x="341.56486" y="242.494256" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.1 -->
      <g transform="translate(348.56486 245.913553) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_76">
      <g>
       <use xlink:href="#m2dedd86f22" x="341.56486" y="197.340861" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.2 -->
      <g transform="translate(348.56486 200.760158) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_77">
      <g>
       <use xlink:href="#m2dedd86f22" x="341.56486" y="152.187467" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.3 -->
      <g transform="translate(348.56486 155.606764) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_78">
      <g>
       <use xlink:href="#m2dedd86f22" x="341.56486" y="107.034073" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.4 -->
      <g transform="translate(348.56486 110.45337) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_53">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p0223db9949">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="p0e965b8a21">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
