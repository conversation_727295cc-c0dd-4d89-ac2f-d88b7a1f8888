<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="926.240887pt" height="423.992062pt" viewBox="0 0 926.240887 423.992062" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:47.264936</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 423.992062 
L 926.240887 423.992062 
L 926.240887 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 43.201875 383.423 
L 847.056875 383.423 
L 847.056875 57.399 
L 43.201875 57.399 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p0edf85c5ab)">
    <image xlink:href="data:image/png;base64,
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" id="image95fb3bd4bd" transform="scale(1 -1) translate(0 -326.4)" x="43.201875" y="-57.023" width="804" height="326.4"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="md5ccdd1db7" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#md5ccdd1db7" x="168.065125" y="383.423" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 1 -->
      <g transform="translate(165.202 397.261594) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#md5ccdd1db7" x="306.787944" y="383.423" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 2 -->
      <g transform="translate(303.924819 397.261594) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#md5ccdd1db7" x="445.510763" y="383.423" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 3 -->
      <g transform="translate(442.647638 397.261594) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#md5ccdd1db7" x="584.233581" y="383.423" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 4 -->
      <g transform="translate(581.370456 397.261594) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#md5ccdd1db7" x="722.9564" y="383.423" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 5 -->
      <g transform="translate(720.093275 397.261594) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="text_6">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(346.692969 413.771125) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_6">
      <defs>
       <path id="md6b962b803" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#md6b962b803" x="43.201875" y="383.423" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 0 -->
      <g transform="translate(30.475625 386.842297) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_7">
      <g>
       <use xlink:href="#md6b962b803" x="43.201875" y="339.953133" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 2 -->
      <g transform="translate(30.475625 343.37243) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_8">
      <g>
       <use xlink:href="#md6b962b803" x="43.201875" y="296.483267" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 4 -->
      <g transform="translate(30.475625 299.902564) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_9">
      <g>
       <use xlink:href="#md6b962b803" x="43.201875" y="253.0134" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 6 -->
      <g transform="translate(30.475625 256.432697) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_10">
      <g>
       <use xlink:href="#md6b962b803" x="43.201875" y="209.543533" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 8 -->
      <g transform="translate(30.475625 212.96283) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_11">
      <g>
       <use xlink:href="#md6b962b803" x="43.201875" y="166.073667" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 10 -->
      <g transform="translate(24.749375 169.492964) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-30" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_12">
      <g>
       <use xlink:href="#md6b962b803" x="43.201875" y="122.6038" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 12 -->
      <g transform="translate(24.749375 126.023097) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-32" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_13">
      <g>
       <use xlink:href="#md6b962b803" x="43.201875" y="79.133933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 14 -->
      <g transform="translate(24.749375 82.55323) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-34" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="text_15">
     <!-- Neuron ID -->
     <g transform="translate(17.837812 260.096625) rotate(-90) scale(0.14 -0.14)">
      <defs>
       <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="340.722656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="411.914062"/>
      <use xlink:href="#DejaVuSans-Bold-49" x="446.728516"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="483.935547"/>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 43.201875 383.423 
L 43.201875 57.399 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 43.201875 383.423 
L 847.056875 383.423 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_16">
    <!-- Population Code for Inter-Agent Distance -->
    <g transform="translate(258.588125 19.3575) scale(0.16 -0.16)">
     <defs>
      <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-64" d="M 2919 2988 
L 2919 4863 
L 4044 4863 
L 4044 0 
L 2919 0 
L 2919 506 
Q 2688 197 2409 53 
Q 2131 -91 1766 -91 
Q 1119 -91 703 423 
Q 288 938 288 1747 
Q 288 2556 703 3070 
Q 1119 3584 1766 3584 
Q 2128 3584 2408 3439 
Q 2688 3294 2919 2988 
z
M 2181 722 
Q 2541 722 2730 984 
Q 2919 1247 2919 1747 
Q 2919 2247 2730 2509 
Q 2541 2772 2181 2772 
Q 1825 2772 1636 2509 
Q 1447 2247 1447 1747 
Q 1447 1247 1636 984 
Q 1825 722 2181 722 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-66" d="M 2841 4863 
L 2841 4128 
L 2222 4128 
Q 1984 4128 1890 4042 
Q 1797 3956 1797 3744 
L 1797 3500 
L 2753 3500 
L 2753 2700 
L 1797 2700 
L 1797 0 
L 678 0 
L 678 2700 
L 122 2700 
L 122 3500 
L 678 3500 
L 678 3744 
Q 678 4316 997 4589 
Q 1316 4863 1984 4863 
L 2841 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="141.992188"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="213.574219"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="284.765625"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="319.042969"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="386.523438"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="434.326172"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="468.603516"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="537.304688"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="608.496094"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="643.310547"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="716.699219"/>
     <use xlink:href="#DejaVuSans-Bold-64" x="785.400391"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="856.982422"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="924.804688"/>
     <use xlink:href="#DejaVuSans-Bold-66" x="959.619141"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1003.125"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1071.826172"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1121.142578"/>
     <use xlink:href="#DejaVuSans-Bold-49" x="1155.957031"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1193.164062"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1264.355469"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1312.158203"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1379.980469"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="1429.296875"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="1470.800781"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="1548.193359"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1619.775391"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1687.597656"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1758.789062"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1806.591797"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="1841.40625"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1924.414062"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="1958.691406"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="2018.212891"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="2066.015625"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="2133.496094"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="2204.6875"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="2263.964844"/>
    </g>
    <!-- Tiling of Distance Space by Neural Population -->
    <g transform="translate(238.526875 37.399) scale(0.16 -0.16)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-62" d="M 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
z
M 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
L 1656 0 
L 538 0 
L 538 4863 
L 1656 4863 
L 1656 2988 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-54"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="68.212891"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="102.490234"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="136.767578"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="171.044922"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="242.236328"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="313.818359"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="348.632812"/>
     <use xlink:href="#DejaVuSans-Bold-66" x="417.333984"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="460.839844"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="495.654297"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="578.662109"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="612.939453"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="672.460938"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="720.263672"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="787.744141"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="858.935547"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="918.212891"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="986.035156"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="1020.849609"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="1092.871094"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="1164.453125"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="1231.933594"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1291.210938"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1359.033203"/>
     <use xlink:href="#DejaVuSans-Bold-62" x="1393.847656"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1465.429688"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1530.615234"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1565.429688"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1649.121094"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1716.943359"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1788.134766"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="1837.451172"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1904.931641"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1939.208984"/>
     <use xlink:href="#DejaVuSans-Bold-50" x="1974.023438"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="2047.314453"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="2116.015625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="2187.597656"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="2258.789062"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="2293.066406"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="2360.546875"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="2408.349609"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="2442.626953"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="2511.328125"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_5">
    <path d="M 866.426875 383.423 
L 882.728075 383.423 
L 882.728075 57.399 
L 866.426875 57.399 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAACIAAAKnCAYAAADjpZ+AAAAESUlEQVR4nO2csXUjMAzFKB33X9lXpRA8gFEAE+CJ5BdlOzkz+xkB99cCf+yZ82uHmRGdiEZk51Sah7W4OCxGJFKOEI3IznG4OCymHPlGI7KnZn3RiOzMv187zIzpRGpWoBHZI3FxWIxIpKkh7SNEI1KzEo1IpSFdekQjUrMSjUilIeUI0YjsOT3CHzQi5QjZK3FxWIxIpGYlGpFKQ/b0yfOLRqRmJRqRtnjS8kw0Iq2KpGQlGpH2EaIRKUdIlx7RiLShEY1IOUK69IhGpBwhGpG93b4vNSvRiOwt4l80Ins/DheHxZQj32hEyhGiEWlqSC89ohGpNEQj0j5CSlaiESlHiEZkb/834GVPyfqiESlHSONLNCKVhmhEWhVJzUo0IpWGaET29B/iXmpWohFZi4nFwyPSP+8je0vWF41IEU80IisZGtGJ1KxAI1KzEo1IU0P2Og7EcyIakR7hRCPSk5N06RGNSJce0Yg0NaR9hGhE+jCPaESaGtKJEI3ISiojOpGaFWhEeoQTjUiPcNJzgmhEalaiEWlqSPsI0YiUI0QjUo6QcoRoRHqEE41IU0P65JloRGpWohEp4knNSjQilYZoRPbM59cOM2M6kZoVaES69IhGpKkhfZhHNCJ9mEcaX6IR6dIjGpGmhnTpEY1Ilx7RiOztEf5SsxKNSBFPNCLtI6TlmWhE9p4uvQeNSFNDSlaiEWkfIRqRPUX8S81KNCJFPNGI9CMFUrMSjUilIRqRcoTUrEQjUrMSjUhTQ3rXEI1Ij3BSsxKNSF8KEI1IXwqQmpVoRNrQiEakdw2pWYlGpGYlGpFuX1KOEI1IOUI0IuUI6cfXRCPSXwoQjUg5QsoRohEpR4hGpO9rSO8aohGpWYlGpH2E1KxEI9JX8qRmJRqRkpVoRJoaUrMSjUifjxCNyJ7b1DyUI0Qj0qVHNCKVhnTpEY3I3i69F41IOULKEaIRaXkmGpFyhJQjRCNSjhCNSDlC9khUJBoikUpDNCLlCKlZiUZkp33kRSPSFk/KEaIRWYuKRKNm/UYjUrISjUg5QsoRohGpWYlGpKkhNSvRiOyR/DWY5kQ0IjuOyphORKIi0RCJlCNEI9LUkJ2a9UUj0vJMNCJNDelEiEakHCEakR3JZ+CiEylHXjQilYZoRCoN6RFONCI1K9GIVBrSiRCNSKUhPSeIRqR9hGhEyhHSiRCNSKUhGpGd63BxWEzN+o1GpNIQjUhbPClZiUakHCEakUpDyhGiEalZiUak0pAdyU9qHBYjEqlZiUak25fUrEQjUrMSjUhTQ9pHiEakZiUlK9GIVBqiESlHSM1KNCJ9g0U0Ik0N6USIRqRLj2hEmhrSiRCNSPsI0Yjsp6l5KUeIRqTSEI1IqyKpWYlGpO9riEakqSGdCNGIdOkRjUhTQ/bTpfeiEalZiUak0pBOhGhEKg3pRIhGpC8FiEakqSGdCNGIVBqiEfkPTQU2A8MOQkkAAAAASUVORK5CYII=" id="image87dae46d19" transform="scale(1 -1) translate(0 -325.92)" x="866.4" y="-57.12" width="16.32" height="325.92"/>
   <g id="matplotlib.axis_3"/>
   <g id="matplotlib.axis_4">
    <g id="ytick_9">
     <g id="line2d_14">
      <defs>
       <path id="mcbb08466d2" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mcbb08466d2" x="882.728075" y="383.423" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 0.0 -->
      <g transform="translate(889.728075 386.842297) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_15">
      <g>
       <use xlink:href="#mcbb08466d2" x="882.728075" y="314.703172" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 0.2 -->
      <g transform="translate(889.728075 318.122469) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_16">
      <g>
       <use xlink:href="#mcbb08466d2" x="882.728075" y="245.983344" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 0.4 -->
      <g transform="translate(889.728075 249.402641) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_17">
      <g>
       <use xlink:href="#mcbb08466d2" x="882.728075" y="177.263516" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 0.6 -->
      <g transform="translate(889.728075 180.682813) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_18">
      <g>
       <use xlink:href="#mcbb08466d2" x="882.728075" y="108.543689" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_21">
      <!-- 0.8 -->
      <g transform="translate(889.728075 111.962986) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_22">
     <!-- Normalized Neural Activation -->
     <g transform="translate(909.922762 132.738812) rotate(-270) scale(0.12 -0.12)">
      <defs>
       <path id="DejaVuSans-4e" d="M 628 4666 
L 1478 4666 
L 3547 763 
L 3547 4666 
L 4159 4666 
L 4159 0 
L 3309 0 
L 1241 3903 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-7a" d="M 353 3500 
L 3084 3500 
L 3084 2975 
L 922 459 
L 3084 459 
L 3084 0 
L 275 0 
L 275 525 
L 2438 3041 
L 353 3041 
L 353 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4e"/>
      <use xlink:href="#DejaVuSans-6f" x="74.804688"/>
      <use xlink:href="#DejaVuSans-72" x="135.986328"/>
      <use xlink:href="#DejaVuSans-6d" x="175.349609"/>
      <use xlink:href="#DejaVuSans-61" x="272.761719"/>
      <use xlink:href="#DejaVuSans-6c" x="334.041016"/>
      <use xlink:href="#DejaVuSans-69" x="361.824219"/>
      <use xlink:href="#DejaVuSans-7a" x="389.607422"/>
      <use xlink:href="#DejaVuSans-65" x="442.097656"/>
      <use xlink:href="#DejaVuSans-64" x="503.621094"/>
      <use xlink:href="#DejaVuSans-20" x="567.097656"/>
      <use xlink:href="#DejaVuSans-4e" x="598.884766"/>
      <use xlink:href="#DejaVuSans-65" x="673.689453"/>
      <use xlink:href="#DejaVuSans-75" x="735.212891"/>
      <use xlink:href="#DejaVuSans-72" x="798.591797"/>
      <use xlink:href="#DejaVuSans-61" x="839.705078"/>
      <use xlink:href="#DejaVuSans-6c" x="900.984375"/>
      <use xlink:href="#DejaVuSans-20" x="928.767578"/>
      <use xlink:href="#DejaVuSans-41" x="960.554688"/>
      <use xlink:href="#DejaVuSans-63" x="1027.212891"/>
      <use xlink:href="#DejaVuSans-74" x="1082.193359"/>
      <use xlink:href="#DejaVuSans-69" x="1121.402344"/>
      <use xlink:href="#DejaVuSans-76" x="1149.185547"/>
      <use xlink:href="#DejaVuSans-61" x="1208.365234"/>
      <use xlink:href="#DejaVuSans-74" x="1269.644531"/>
      <use xlink:href="#DejaVuSans-69" x="1308.853516"/>
      <use xlink:href="#DejaVuSans-6f" x="1336.636719"/>
      <use xlink:href="#DejaVuSans-6e" x="1397.818359"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_6">
    <path d="M 866.426875 383.423 
L 874.577475 383.423 
L 882.728075 383.423 
L 882.728075 57.399 
L 874.577475 57.399 
L 866.426875 57.399 
L 866.426875 383.423 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
  <g id="axes_3">
   <g id="matplotlib.axis_5">
    <g id="ytick_14">
     <g id="line2d_19">
      <g>
       <use xlink:href="#mcbb08466d2" x="847.056875" y="383.423" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_23">
      <!-- 0 -->
      <g transform="translate(854.056875 386.842297) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_20">
      <g>
       <use xlink:href="#mcbb08466d2" x="847.056875" y="339.953133" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_24">
      <!-- 2 -->
      <g transform="translate(854.056875 343.37243) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_21">
      <g>
       <use xlink:href="#mcbb08466d2" x="847.056875" y="296.483267" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_25">
      <!-- 4 -->
      <g transform="translate(854.056875 299.902564) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_22">
      <g>
       <use xlink:href="#mcbb08466d2" x="847.056875" y="253.0134" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 6 -->
      <g transform="translate(854.056875 256.432697) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mcbb08466d2" x="847.056875" y="209.543533" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 8 -->
      <g transform="translate(854.056875 212.96283) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mcbb08466d2" x="847.056875" y="166.073667" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 10 -->
      <g transform="translate(854.056875 169.492964) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-30" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_25">
      <g>
       <use xlink:href="#mcbb08466d2" x="847.056875" y="122.6038" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 12 -->
      <g transform="translate(854.056875 126.023097) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-32" x="63.623047"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_26">
      <g>
       <use xlink:href="#mcbb08466d2" x="847.056875" y="79.133933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 14 -->
      <g transform="translate(854.056875 82.55323) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-34" x="63.623047"/>
      </g>
     </g>
    </g>
   </g>
   <g id="patch_7">
    <path d="M 43.201875 383.423 
L 43.201875 57.399 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_8">
    <path d="M 43.201875 383.423 
L 847.056875 383.423 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p0edf85c5ab">
   <rect x="43.201875" y="57.399" width="803.855" height="326.024"/>
  </clipPath>
 </defs>
</svg>
