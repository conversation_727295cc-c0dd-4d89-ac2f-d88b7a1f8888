<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="826.280413pt" height="352.267438pt" viewBox="0 0 826.280413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:36.440991</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 826.280413 352.267438 
L 826.280413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#pf24ca0cda9)">
    <image xlink:href="data:image/png;base64,
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" id="image3c8a21de11" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#pf24ca0cda9)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#pf24ca0cda9)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#pf24ca0cda9)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m588248d2dd" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m588248d2dd" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m588248d2dd" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m588248d2dd" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m588248d2dd" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m588248d2dd" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m588248d2dd" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m588248d2dd" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m588248d2dd" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m588248d2dd" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m157ff31200" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m157ff31200" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m157ff31200" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m157ff31200" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m157ff31200" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m157ff31200" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m157ff31200" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m157ff31200" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m157ff31200" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#pf24ca0cda9)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m157ff31200" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Close-Distance Cell (Neuron 51) -->
    <g transform="translate(69.464094 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-43"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="73.388672"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="107.666016"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="176.367188"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="235.888672"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="303.710938"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="345.214844"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="428.222656"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="462.5"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="522.021484"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="569.824219"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="637.304688"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="708.496094"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="767.773438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="835.595703"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="870.410156"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="943.798828"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1011.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1045.898438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1080.175781"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1114.990234"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1160.693359"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1244.384766"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1312.207031"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1383.398438"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1432.714844"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1501.416016"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1572.607422"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1607.421875"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="1677.001953"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1746.582031"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m99060a6f58" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pf24ca0cda9)">
     <use xlink:href="#m99060a6f58" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m99060a6f58" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 441.389015 314.62525 
L 816.217288 314.62525 
L 816.217288 44.84925 
L 441.389015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m21b083ecdb" d="M 446.070075 -37.642188 
L 446.070075 -37.642188 
L 455.432194 -189.702248 
L 464.794313 -277.663859 
L 474.156433 -250.599945 
L 483.518552 -294.571711 
L 492.880671 -245.533961 
L 502.242791 -214.899547 
L 511.60491 -138.311919 
L 520.967029 -174.390001 
L 530.329148 -99.983242 
L 539.691268 -109.281006 
L 549.053387 -187.042265 
L 558.415506 -205.302648 
L 567.777625 -203.816033 
L 577.139745 -129.751036 
L 586.501864 -117.853442 
L 595.863983 -93.406655 
L 605.226102 -97.978182 
L 614.588222 -71.718223 
L 623.950341 -71.661839 
L 633.31246 -65.227811 
L 642.674579 -55.90292 
L 652.036699 -49.273367 
L 661.398818 -47.599261 
L 670.760937 -42.246528 
L 680.123056 -39.025724 
L 689.485176 -39.094453 
L 698.847295 -38.618737 
L 708.209414 -37.97292 
L 717.571533 -37.731331 
L 717.571533 -37.642188 
L 717.571533 -37.642188 
L 708.209414 -37.642188 
L 698.847295 -37.642188 
L 689.485176 -37.642188 
L 680.123056 -37.642188 
L 670.760937 -37.642188 
L 661.398818 -37.642188 
L 652.036699 -37.642188 
L 642.674579 -37.642188 
L 633.31246 -37.642188 
L 623.950341 -37.642188 
L 614.588222 -37.642188 
L 605.226102 -37.642188 
L 595.863983 -37.642188 
L 586.501864 -37.642188 
L 577.139745 -37.642188 
L 567.777625 -37.642188 
L 558.415506 -37.642188 
L 549.053387 -37.642188 
L 539.691268 -37.642188 
L 530.329148 -37.642188 
L 520.967029 -37.642188 
L 511.60491 -37.642188 
L 502.242791 -37.642188 
L 492.880671 -37.642188 
L 483.518552 -37.642188 
L 474.156433 -37.642188 
L 464.794313 -37.642188 
L 455.432194 -37.642188 
L 446.070075 -37.642188 
z
" style="stroke: #e31a1c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#p42057fa8db)">
     <use xlink:href="#m21b083ecdb" x="0" y="352.267438" style="fill: #e31a1c; fill-opacity: 0.3; stroke: #e31a1c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 441.389015 314.62525 
L 441.389015 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m588248d2dd" x="441.389015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(438.52589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 488.242549 314.62525 
L 488.242549 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m588248d2dd" x="488.242549" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(485.379424 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 535.096084 314.62525 
L 535.096084 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m588248d2dd" x="535.096084" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(532.232959 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 581.949618 314.62525 
L 581.949618 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m588248d2dd" x="581.949618" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(579.086493 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 628.803152 314.62525 
L 628.803152 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m588248d2dd" x="628.803152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(625.940027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 675.656686 314.62525 
L 675.656686 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m588248d2dd" x="675.656686" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(672.793561 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 722.51022 314.62525 
L 722.51022 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m588248d2dd" x="722.51022" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(719.647095 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 769.363754 314.62525 
L 769.363754 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m588248d2dd" x="769.363754" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(766.500629 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 816.217288 314.62525 
L 816.217288 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m588248d2dd" x="816.217288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(813.354163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(551.460261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 441.389015 314.62525 
L 816.217288 314.62525 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m157ff31200" x="441.389015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(420.076203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 441.389015 270.473212 
L 816.217288 270.473212 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m157ff31200" x="441.389015" y="270.473212" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.1 -->
      <g transform="translate(420.076203 273.892509) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 441.389015 226.321175 
L 816.217288 226.321175 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m157ff31200" x="441.389015" y="226.321175" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.2 -->
      <g transform="translate(420.076203 229.740471) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 441.389015 182.169137 
L 816.217288 182.169137 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m157ff31200" x="441.389015" y="182.169137" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.3 -->
      <g transform="translate(420.076203 185.588434) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 441.389015 138.017099 
L 816.217288 138.017099 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m157ff31200" x="441.389015" y="138.017099" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.4 -->
      <g transform="translate(420.076203 141.436396) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 441.389015 93.865061 
L 816.217288 93.865061 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m157ff31200" x="441.389015" y="93.865061" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.5 -->
      <g transform="translate(420.076203 97.284358) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 441.389015 49.713024 
L 816.217288 49.713024 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m157ff31200" x="441.389015" y="49.713024" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 0.6 -->
      <g transform="translate(420.076203 53.132321) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_43">
     <!-- Neural Activation -->
     <g transform="translate(413.788547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_69">
    <path d="M 446.070075 314.62525 
L 455.432194 162.56519 
L 464.794313 74.603578 
L 474.156433 101.667492 
L 483.518552 57.695726 
L 492.880671 106.733476 
L 502.242791 137.367891 
L 511.60491 213.955518 
L 520.967029 177.877436 
L 530.329148 252.284195 
L 539.691268 242.986432 
L 549.053387 165.225173 
L 558.415506 146.964789 
L 567.777625 148.451404 
L 577.139745 222.516402 
L 586.501864 234.413995 
L 595.863983 258.860782 
L 605.226102 254.289255 
L 614.588222 280.549215 
L 623.950341 280.605598 
L 633.31246 287.039626 
L 642.674579 296.364517 
L 652.036699 302.99407 
L 661.398818 304.668177 
L 670.760937 310.02091 
L 680.123056 313.241713 
L 689.485176 313.172984 
L 698.847295 313.6487 
L 708.209414 314.294518 
L 717.571533 314.536107 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_70">
    <path d="M 483.518552 314.62525 
L 483.518552 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #e31a1c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_71">
    <path d="M 581.949618 314.62525 
L 581.949618 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_72">
    <path d="M 675.656686 314.62525 
L 675.656686 44.84925 
" clip-path="url(#p42057fa8db)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 441.389015 314.62525 
L 441.389015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 441.389015 314.62525 
L 816.217288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_44">
    <g id="patch_15">
     <path d="M 703.034624 114.0718 
L 797.475874 114.0718 
Q 800.675874 114.0718 800.675874 110.8718 
L 800.675874 58.33805 
Q 800.675874 55.13805 797.475874 55.13805 
L 703.034624 55.13805 
Q 699.834624 55.13805 699.834624 58.33805 
L 699.834624 110.8718 
Q 699.834624 114.0718 703.034624 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 0.9m -->
    <g transform="translate(715.209624 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-30" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-39" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.582 -->
    <g transform="translate(710.202124 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-35" x="900.056641"/>
     <use xlink:href="#DejaVuSans-38" x="963.679688"/>
     <use xlink:href="#DejaVuSans-32" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.492 -->
    <g transform="translate(737.305874 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-34" x="561.255859"/>
     <use xlink:href="#DejaVuSans-39" x="624.878906"/>
     <use xlink:href="#DejaVuSans-32" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.07 -->
    <g transform="translate(704.488374 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-30" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-37" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 1.073 -->
    <g transform="translate(703.034624 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-31" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-30" x="989.607422"/>
     <use xlink:href="#DejaVuSans-37" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-33" x="1116.853516"/>
    </g>
    <!-- Peak Width: 2.4m -->
    <g transform="translate(726.952124 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-32" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-34" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_45">
    <!-- Distance Tuning Curve -->
    <g transform="translate(552.989402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 0.9m, Sparsity: 0.492 -->
    <g transform="translate(535.064402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m18d38e4af4" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p42057fa8db)">
     <use xlink:href="#m18d38e4af4" x="483.518552" y="57.695726" style="fill: #e31a1c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 693.709788 86.47675 
L 810.617288 86.47675 
Q 812.217288 86.47675 812.217288 84.87675 
L 812.217288 50.44925 
Q 812.217288 48.84925 810.617288 48.84925 
L 693.709788 48.84925 
Q 692.109788 48.84925 692.109788 50.44925 
L 692.109788 84.87675 
Q 692.109788 86.47675 693.709788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_73">
     <path d="M 695.309788 55.328 
L 703.309788 55.328 
L 711.309788 55.328 
" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_46">
     <!-- Tuning Curve -->
     <g transform="translate(717.709788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_74">
     <path d="M 695.309788 67.0705 
L 703.309788 67.0705 
L 711.309788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(717.709788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_75">
     <path d="M 695.309788 78.813 
L 703.309788 78.813 
L 711.309788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_48">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(717.709788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.63132 287.64765 
L 341.42236 287.64765 
L 341.42236 71.82685 
L 330.63132 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABYAAAHBCAYAAABt+EsYAAACDUlEQVR4nO2bS25CQQwEDUzunXsHQhYz9MsFelFS1QGskrv9+AhuM9/vKbBmVmPu3CtTZ2bNfFUGN43dcQa7io11C9YtIOvGM+60wlUE6xasW7BuwR0HX/4Dsm48Y5/HG96B3GbelS9CasYOBg9ec+sMLho/WoM7D7fmKjoPN40vrFsghscztm4HYng8Y+t2IIbHM7ZuB2J4PGPrdiCGxzO2bgdieDzjUt2KxqUeE8PT+MA7EJ/HQeNQM3YVQePg0y0Qw9O4PZi3Co2DT7dADE/ja3Dl1zzVVbwqgzUO6/54dgavL9oqHrjwiMatuvFWcccZj8abpnHrQICr0Hhj3YLhBY3/D+70uLcKD+QDz9i6BV54PGPrFnjh8YyRq/BANtYtWLdg3fqDeavwQELtQJDh8Yyt2xlMDE/jjQcSPJCANLZuZzAxPJ6xdTuDieFpvOEdiF8rBI2DdQvI8DTOYN9XbHjhFY1frbo9cXV7/VYGF8PrrBhovG6dGjf/XYELT+NrsHU7aBxqrSCuQuMDr268HRte4IXHMza8oHGwbsHwAu8VhLgKnrEn3R7MW4V1C9YtGF7gGRNXYd0OPGPDCxoHPyoEjQOvbrwdG17ghcczNrygcbBuYf3gwiv9jq6549a7TeAqcMYl4eaOaYMNL7jja7Cr+GDdgnUL1i3wjA0veCDBugXrFng75q3iD9EGue+xdPB4AAAAAElFTkSuQmCC" id="imageaf8c467c36" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="10.56" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_17">
     <g id="line2d_76">
      <defs>
       <path id="mb7f3918b07" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mb7f3918b07" x="341.42236" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.00 -->
      <g transform="translate(348.42236 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_77">
      <g>
       <use xlink:href="#mb7f3918b07" x="341.42236" y="263.440312" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.05 -->
      <g transform="translate(348.42236 266.859608) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_78">
      <g>
       <use xlink:href="#mb7f3918b07" x="341.42236" y="239.232973" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.10 -->
      <g transform="translate(348.42236 242.65227) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_79">
      <g>
       <use xlink:href="#mb7f3918b07" x="341.42236" y="215.025635" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.15 -->
      <g transform="translate(348.42236 218.444932) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_80">
      <g>
       <use xlink:href="#mb7f3918b07" x="341.42236" y="190.818296" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.20 -->
      <g transform="translate(348.42236 194.237593) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_81">
      <g>
       <use xlink:href="#mb7f3918b07" x="341.42236" y="166.610958" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.25 -->
      <g transform="translate(348.42236 170.030255) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_82">
      <g>
       <use xlink:href="#mb7f3918b07" x="341.42236" y="142.403619" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 0.30 -->
      <g transform="translate(348.42236 145.822916) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_83">
      <g>
       <use xlink:href="#mb7f3918b07" x="341.42236" y="118.196281" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 0.35 -->
      <g transform="translate(348.42236 121.615578) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_84">
      <g>
       <use xlink:href="#mb7f3918b07" x="341.42236" y="93.988942" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_57">
      <!-- 0.40 -->
      <g transform="translate(348.42236 97.408239) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_58">
     <!-- Neural Activation -->
     <g transform="translate(375.103141 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.63132 287.64765 
L 336.02684 287.64765 
L 341.42236 287.64765 
L 341.42236 71.82685 
L 336.02684 71.82685 
L 330.63132 71.82685 
L 330.63132 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pf24ca0cda9">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="p42057fa8db">
   <rect x="441.389015" y="44.84925" width="374.828273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
