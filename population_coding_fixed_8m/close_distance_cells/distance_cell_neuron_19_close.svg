<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:37.548903</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p0291d2b736)">
    <image xlink:href="data:image/png;base64,
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" id="image2bfafcf38c" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p0291d2b736)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p0291d2b736)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p0291d2b736)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="me6871e6259" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#me6871e6259" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#me6871e6259" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#me6871e6259" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#me6871e6259" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#me6871e6259" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#me6871e6259" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#me6871e6259" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#me6871e6259" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#me6871e6259" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m49d4e2d2d3" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m49d4e2d2d3" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p0291d2b736)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Close-Distance Cell (Neuron 19) -->
    <g transform="translate(69.464094 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-43"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="73.388672"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="107.666016"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="176.367188"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="235.888672"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="303.710938"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="345.214844"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="428.222656"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="462.5"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="522.021484"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="569.824219"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="637.304688"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="708.496094"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="767.773438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="835.595703"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="870.410156"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="943.798828"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1011.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1045.898438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1080.175781"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1114.990234"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1160.693359"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1244.384766"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1312.207031"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1383.398438"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1432.714844"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1501.416016"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1572.607422"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="1607.421875"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="1677.001953"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1746.582031"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m1b2e395e62" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p0291d2b736)">
     <use xlink:href="#m1b2e395e62" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m1b2e395e62" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m4ae5e8c12b" d="M 440.975667 -37.642188 
L 440.975667 -37.642188 
L 450.408971 -294.571711 
L 459.842275 -271.998241 
L 469.275579 -261.277882 
L 478.708883 -196.681819 
L 488.142187 -204.409518 
L 497.575491 -173.787874 
L 507.008795 -195.150434 
L 516.442099 -168.794536 
L 525.875403 -136.92412 
L 535.308707 -154.070588 
L 544.742011 -102.948613 
L 554.175315 -91.874781 
L 563.608619 -105.239438 
L 573.041923 -114.594764 
L 582.475227 -98.927718 
L 591.908531 -100.680472 
L 601.341835 -95.826122 
L 610.775139 -63.678345 
L 620.208443 -51.178583 
L 629.641747 -63.55734 
L 639.075051 -69.949879 
L 648.508355 -76.913223 
L 657.941658 -81.992081 
L 667.374962 -57.755794 
L 676.808266 -67.494507 
L 686.24157 -59.23762 
L 695.674874 -63.052622 
L 705.108178 -64.606151 
L 714.541482 -83.11814 
L 714.541482 -37.642188 
L 714.541482 -37.642188 
L 705.108178 -37.642188 
L 695.674874 -37.642188 
L 686.24157 -37.642188 
L 676.808266 -37.642188 
L 667.374962 -37.642188 
L 657.941658 -37.642188 
L 648.508355 -37.642188 
L 639.075051 -37.642188 
L 629.641747 -37.642188 
L 620.208443 -37.642188 
L 610.775139 -37.642188 
L 601.341835 -37.642188 
L 591.908531 -37.642188 
L 582.475227 -37.642188 
L 573.041923 -37.642188 
L 563.608619 -37.642188 
L 554.175315 -37.642188 
L 544.742011 -37.642188 
L 535.308707 -37.642188 
L 525.875403 -37.642188 
L 516.442099 -37.642188 
L 507.008795 -37.642188 
L 497.575491 -37.642188 
L 488.142187 -37.642188 
L 478.708883 -37.642188 
L 469.275579 -37.642188 
L 459.842275 -37.642188 
L 450.408971 -37.642188 
L 440.975667 -37.642188 
z
" style="stroke: #e31a1c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pa719eca177)">
     <use xlink:href="#m4ae5e8c12b" x="0" y="352.267438" style="fill: #e31a1c; fill-opacity: 0.3; stroke: #e31a1c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#me6871e6259" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#me6871e6259" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#me6871e6259" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#me6871e6259" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#me6871e6259" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#me6871e6259" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#me6871e6259" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#me6871e6259" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#me6871e6259" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 282.744505 
L 813.937288 282.744505 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="436.259015" y="282.744505" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.2 -->
      <g transform="translate(414.946203 286.163801) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 250.863759 
L 813.937288 250.863759 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="436.259015" y="250.863759" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.4 -->
      <g transform="translate(414.946203 254.283056) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 218.983014 
L 813.937288 218.983014 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="436.259015" y="218.983014" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.6 -->
      <g transform="translate(414.946203 222.402311) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 187.102268 
L 813.937288 187.102268 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="436.259015" y="187.102268" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.8 -->
      <g transform="translate(414.946203 190.521565) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 155.221523 
L 813.937288 155.221523 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="436.259015" y="155.221523" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 1.0 -->
      <g transform="translate(414.946203 158.64082) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 436.259015 123.340777 
L 813.937288 123.340777 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="436.259015" y="123.340777" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 1.2 -->
      <g transform="translate(414.946203 126.760074) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 436.259015 91.460032 
L 813.937288 91.460032 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="436.259015" y="91.460032" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 1.4 -->
      <g transform="translate(414.946203 94.879329) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_71">
      <path d="M 436.259015 59.579286 
L 813.937288 59.579286 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_72">
      <g>
       <use xlink:href="#m49d4e2d2d3" x="436.259015" y="59.579286" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_44">
      <!-- 1.6 -->
      <g transform="translate(414.946203 62.998583) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_45">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_73">
    <path d="M 440.975667 314.62525 
L 450.408971 57.695726 
L 459.842275 80.269196 
L 469.275579 90.989555 
L 478.708883 155.585618 
L 488.142187 147.85792 
L 497.575491 178.479564 
L 507.008795 157.117003 
L 516.442099 183.472902 
L 525.875403 215.343318 
L 535.308707 198.196849 
L 544.742011 249.318825 
L 554.175315 260.392657 
L 563.608619 247.027999 
L 573.041923 237.672673 
L 582.475227 253.339719 
L 591.908531 251.586966 
L 601.341835 256.441316 
L 610.775139 288.589093 
L 620.208443 301.088855 
L 629.641747 288.710098 
L 639.075051 282.317558 
L 648.508355 275.354215 
L 657.941658 270.275356 
L 667.374962 294.511644 
L 676.808266 284.77293 
L 686.24157 293.029817 
L 695.674874 289.214816 
L 705.108178 287.661286 
L 714.541482 269.149297 
" clip-path="url(#pa719eca177)" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_74">
    <path d="M 450.408971 314.62525 
L 450.408971 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #e31a1c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_75">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_76">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pa719eca177)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_46">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 0.3m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-30" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-33" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 1.612 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-31" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-36" x="900.056641"/>
     <use xlink:href="#DejaVuSans-31" x="963.679688"/>
     <use xlink:href="#DejaVuSans-32" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.426 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-34" x="561.255859"/>
     <use xlink:href="#DejaVuSans-32" x="624.878906"/>
     <use xlink:href="#DejaVuSans-36" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.32 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-33" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-32" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.615 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-36" x="989.607422"/>
     <use xlink:href="#DejaVuSans-31" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-35" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.4m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-34" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_47">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 0.3m, Sparsity: 0.426 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-36" d="M 2316 2303 
Q 2000 2303 1842 2098 
Q 1684 1894 1684 1484 
Q 1684 1075 1842 870 
Q 2000 666 2316 666 
Q 2634 666 2792 870 
Q 2950 1075 2950 1484 
Q 2950 1894 2792 2098 
Q 2634 2303 2316 2303 
z
M 3803 4544 
L 3803 3681 
Q 3506 3822 3243 3889 
Q 2981 3956 2731 3956 
Q 2194 3956 1894 3657 
Q 1594 3359 1544 2772 
Q 1750 2925 1990 3001 
Q 2231 3078 2516 3078 
Q 3231 3078 3670 2659 
Q 4109 2241 4109 1563 
Q 4109 813 3618 361 
Q 3128 -91 2303 -91 
Q 1394 -91 895 523 
Q 397 1138 397 2266 
Q 397 3422 980 4083 
Q 1563 4744 2578 4744 
Q 2900 4744 3203 4694 
Q 3506 4644 3803 4544 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-36" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m53c9078671" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pa719eca177)">
     <use xlink:href="#m53c9078671" x="450.408971" y="57.695726" style="fill: #e31a1c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 691.429788 86.47675 
L 808.337288 86.47675 
Q 809.937288 86.47675 809.937288 84.87675 
L 809.937288 50.44925 
Q 809.937288 48.84925 808.337288 48.84925 
L 691.429788 48.84925 
Q 689.829788 48.84925 689.829788 50.44925 
L 689.829788 84.87675 
Q 689.829788 86.47675 691.429788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_77">
     <path d="M 693.029788 55.328 
L 701.029788 55.328 
L 709.029788 55.328 
" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_48">
     <!-- Tuning Curve -->
     <g transform="translate(715.429788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_78">
     <path d="M 693.029788 67.0705 
L 701.029788 67.0705 
L 709.029788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_49">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(715.429788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_79">
     <path d="M 693.029788 78.813 
L 701.029788 78.813 
L 709.029788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_50">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(715.429788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="imageb9e3c4059b" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_19">
     <g id="line2d_80">
      <defs>
       <path id="m5dc02cfb7b" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m5dc02cfb7b" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_81">
      <g>
       <use xlink:href="#m5dc02cfb7b" x="341.56486" y="257.313065" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.1 -->
      <g transform="translate(348.56486 260.732362) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_82">
      <g>
       <use xlink:href="#m5dc02cfb7b" x="341.56486" y="226.97848" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.2 -->
      <g transform="translate(348.56486 230.397777) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_83">
      <g>
       <use xlink:href="#m5dc02cfb7b" x="341.56486" y="196.643894" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.3 -->
      <g transform="translate(348.56486 200.063191) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_84">
      <g>
       <use xlink:href="#m5dc02cfb7b" x="341.56486" y="166.309309" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 0.4 -->
      <g transform="translate(348.56486 169.728606) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_85">
      <g>
       <use xlink:href="#m5dc02cfb7b" x="341.56486" y="135.974724" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 0.5 -->
      <g transform="translate(348.56486 139.394021) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_86">
      <g>
       <use xlink:href="#m5dc02cfb7b" x="341.56486" y="105.640139" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_57">
      <!-- 0.6 -->
      <g transform="translate(348.56486 109.059436) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_26">
     <g id="line2d_87">
      <g>
       <use xlink:href="#m5dc02cfb7b" x="341.56486" y="75.305554" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_58">
      <!-- 0.7 -->
      <g transform="translate(348.56486 78.724851) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-37" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_59">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p0291d2b736">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pa719eca177">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
