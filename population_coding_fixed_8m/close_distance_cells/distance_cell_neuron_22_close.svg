<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="837.632413pt" height="352.267438pt" viewBox="0 0 837.632413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:32:38.561525</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 837.632413 352.267438 
L 837.632413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#pd23d8e79b6)">
    <image xlink:href="data:image/png;base64,
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" id="image6d0db6d901" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#pd23d8e79b6)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#pd23d8e79b6)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#pd23d8e79b6)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="mb3a15e6c6a" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mb3a15e6c6a" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m576b413a1d" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m576b413a1d" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845938 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m576b413a1d" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845938 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m576b413a1d" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845938 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m576b413a1d" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845938 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m576b413a1d" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m576b413a1d" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m576b413a1d" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m576b413a1d" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#pd23d8e79b6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m576b413a1d" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Close-Distance Cell (Neuron 22) -->
    <g transform="translate(69.464094 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-43"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="73.388672"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="107.666016"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="176.367188"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="235.888672"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="303.710938"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="345.214844"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="428.222656"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="462.5"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="522.021484"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="569.824219"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="637.304688"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="708.496094"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="767.773438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="835.595703"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="870.410156"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="943.798828"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1011.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1045.898438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1080.175781"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1114.990234"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1160.693359"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1244.384766"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1312.207031"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1383.398438"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1432.714844"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1501.416016"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1572.607422"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1607.421875"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1677.001953"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1746.582031"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m99a8dbaddc" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pd23d8e79b6)">
     <use xlink:href="#m99a8dbaddc" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m99a8dbaddc" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 466.931015 314.62525 
L 827.569288 314.62525 
L 827.569288 44.84925 
L 466.931015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="me1e70c962e" d="M 471.434863 -37.642188 
L 471.434863 -37.642188 
L 480.442557 -74.541006 
L 489.450251 -108.059524 
L 498.457946 -135.995836 
L 507.46564 -242.904209 
L 516.473334 -213.403042 
L 525.481029 -144.053172 
L 534.488723 -138.126677 
L 543.496417 -171.011464 
L 552.504112 -53.871075 
L 561.511806 -128.909892 
L 570.5195 -210.728095 
L 579.527195 -294.571711 
L 588.534889 -155.397496 
L 597.542584 -198.013907 
L 606.550278 -47.010783 
L 615.557972 -164.59166 
L 624.565667 -37.642188 
L 633.573361 -110.473989 
L 642.581055 -166.670203 
L 651.58875 -123.631945 
L 660.596444 -72.032498 
L 669.604138 -139.719592 
L 678.611833 -100.291184 
L 687.619527 -67.482001 
L 696.627222 -94.628909 
L 705.634916 -57.730084 
L 714.64261 -60.13604 
L 723.650305 -51.467173 
L 732.657999 -42.135433 
L 732.657999 -37.642188 
L 732.657999 -37.642188 
L 723.650305 -37.642188 
L 714.64261 -37.642188 
L 705.634916 -37.642188 
L 696.627222 -37.642188 
L 687.619527 -37.642188 
L 678.611833 -37.642188 
L 669.604138 -37.642188 
L 660.596444 -37.642188 
L 651.58875 -37.642188 
L 642.581055 -37.642188 
L 633.573361 -37.642188 
L 624.565667 -37.642188 
L 615.557972 -37.642188 
L 606.550278 -37.642188 
L 597.542584 -37.642188 
L 588.534889 -37.642188 
L 579.527195 -37.642188 
L 570.5195 -37.642188 
L 561.511806 -37.642188 
L 552.504112 -37.642188 
L 543.496417 -37.642188 
L 534.488723 -37.642188 
L 525.481029 -37.642188 
L 516.473334 -37.642188 
L 507.46564 -37.642188 
L 498.457946 -37.642188 
L 489.450251 -37.642188 
L 480.442557 -37.642188 
L 471.434863 -37.642188 
z
" style="stroke: #e31a1c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pcaebd83d46)">
     <use xlink:href="#me1e70c962e" x="0" y="352.267438" style="fill: #e31a1c; fill-opacity: 0.3; stroke: #e31a1c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 466.931015 314.62525 
L 466.931015 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="466.931015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(464.06789 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 512.010799 314.62525 
L 512.010799 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="512.010799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(509.147674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 557.090584 314.62525 
L 557.090584 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="557.090584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(554.227459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 602.170368 314.62525 
L 602.170368 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="602.170368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(599.307243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 647.250152 314.62525 
L 647.250152 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="647.250152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(644.387027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 692.329936 314.62525 
L 692.329936 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="692.329936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(689.466811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 737.40972 314.62525 
L 737.40972 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="737.40972" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(734.546595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 782.489504 314.62525 
L 782.489504 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="782.489504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(779.626379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 827.569288 314.62525 
L 827.569288 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#mb3a15e6c6a" x="827.569288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(824.706163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(569.907261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 466.931015 314.62525 
L 827.569288 314.62525 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m576b413a1d" x="466.931015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0000 -->
      <g transform="translate(428.439453 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
       <use xlink:href="#DejaVuSans-30" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 466.931015 281.740548 
L 827.569288 281.740548 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m576b413a1d" x="466.931015" y="281.740548" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.0025 -->
      <g transform="translate(428.439453 285.159845) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-32" x="222.65625"/>
       <use xlink:href="#DejaVuSans-35" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 466.931015 248.855846 
L 827.569288 248.855846 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m576b413a1d" x="466.931015" y="248.855846" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.0050 -->
      <g transform="translate(428.439453 252.275142) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
       <use xlink:href="#DejaVuSans-30" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 466.931015 215.971143 
L 827.569288 215.971143 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m576b413a1d" x="466.931015" y="215.971143" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.0075 -->
      <g transform="translate(428.439453 219.39044) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-37" x="222.65625"/>
       <use xlink:href="#DejaVuSans-35" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 466.931015 183.086441 
L 827.569288 183.086441 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m576b413a1d" x="466.931015" y="183.086441" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.0100 -->
      <g transform="translate(428.439453 186.505738) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
       <use xlink:href="#DejaVuSans-30" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 466.931015 150.201739 
L 827.569288 150.201739 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m576b413a1d" x="466.931015" y="150.201739" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.0125 -->
      <g transform="translate(428.439453 153.621036) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-32" x="222.65625"/>
       <use xlink:href="#DejaVuSans-35" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 466.931015 117.317037 
L 827.569288 117.317037 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m576b413a1d" x="466.931015" y="117.317037" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 0.0150 -->
      <g transform="translate(428.439453 120.736334) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
       <use xlink:href="#DejaVuSans-30" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 466.931015 84.432334 
L 827.569288 84.432334 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m576b413a1d" x="466.931015" y="84.432334" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 0.0175 -->
      <g transform="translate(428.439453 87.851631) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-37" x="222.65625"/>
       <use xlink:href="#DejaVuSans-35" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_71">
      <path d="M 466.931015 51.547632 
L 827.569288 51.547632 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_72">
      <g>
       <use xlink:href="#m576b413a1d" x="466.931015" y="51.547632" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_44">
      <!-- 0.0200 -->
      <g transform="translate(428.439453 54.966929) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-32" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
       <use xlink:href="#DejaVuSans-30" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="text_45">
     <!-- Neural Activation -->
     <g transform="translate(422.151797 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_73">
    <path d="M 471.434863 314.62525 
L 480.442557 277.726431 
L 489.450251 244.207913 
L 498.457946 216.271601 
L 507.46564 109.363228 
L 516.473334 138.864395 
L 525.481029 208.214266 
L 534.488723 214.140761 
L 543.496417 181.255973 
L 552.504112 298.396363 
L 561.511806 223.357545 
L 570.5195 141.539342 
L 579.527195 57.695726 
L 588.534889 196.869942 
L 597.542584 154.25353 
L 606.550278 305.256655 
L 615.557972 187.675778 
L 624.565667 314.62525 
L 633.573361 241.793449 
L 642.581055 185.597234 
L 651.58875 228.635492 
L 660.596444 280.23494 
L 669.604138 212.547846 
L 678.611833 251.976253 
L 687.619527 284.785436 
L 696.627222 257.638529 
L 705.634916 294.537354 
L 714.64261 292.131397 
L 723.650305 300.800264 
L 732.657999 310.132004 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_74">
    <path d="M 579.527195 314.62525 
L 579.527195 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #e31a1c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_75">
    <path d="M 602.170368 314.62525 
L 602.170368 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_76">
    <path d="M 692.329936 314.62525 
L 692.329936 44.84925 
" clip-path="url(#pcaebd83d46)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 466.931015 314.62525 
L 466.931015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 466.931015 314.62525 
L 827.569288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_46">
    <g id="patch_15">
     <path d="M 715.096124 114.0718 
L 809.537374 114.0718 
Q 812.737374 114.0718 812.737374 110.8718 
L 812.737374 58.33805 
Q 812.737374 55.13805 809.537374 55.13805 
L 715.096124 55.13805 
Q 711.896124 55.13805 711.896124 58.33805 
L 711.896124 110.8718 
Q 711.896124 114.0718 715.096124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 2.5m -->
    <g transform="translate(727.271124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-32" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-35" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.020 -->
    <g transform="translate(722.263624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-30" x="900.056641"/>
     <use xlink:href="#DejaVuSans-32" x="963.679688"/>
     <use xlink:href="#DejaVuSans-30" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.391 -->
    <g transform="translate(749.367374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-33" x="561.255859"/>
     <use xlink:href="#DejaVuSans-39" x="624.878906"/>
     <use xlink:href="#DejaVuSans-31" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.56 -->
    <g transform="translate(716.549874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-35" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-36" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.629 -->
    <g transform="translate(715.096124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-36" x="989.607422"/>
     <use xlink:href="#DejaVuSans-32" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-39" x="1116.853516"/>
    </g>
    <!-- Peak Width: 3.0m -->
    <g transform="translate(739.013624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-33" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-30" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_47">
    <!-- Distance Tuning Curve -->
    <g transform="translate(571.436402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 2.5m, Sparsity: 0.391 -->
    <g transform="translate(553.511402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m3afa0d0fe9" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pcaebd83d46)">
     <use xlink:href="#m3afa0d0fe9" x="579.527195" y="57.695726" style="fill: #e31a1c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 705.061788 86.47675 
L 821.969288 86.47675 
Q 823.569288 86.47675 823.569288 84.87675 
L 823.569288 50.44925 
Q 823.569288 48.84925 821.969288 48.84925 
L 705.061788 48.84925 
Q 703.461788 48.84925 703.461788 50.44925 
L 703.461788 84.87675 
Q 703.461788 86.47675 705.061788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_77">
     <path d="M 706.661788 55.328 
L 714.661788 55.328 
L 722.661788 55.328 
" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_48">
     <!-- Tuning Curve -->
     <g transform="translate(729.061788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_78">
     <path d="M 706.661788 67.0705 
L 714.661788 67.0705 
L 722.661788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_49">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(729.061788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_79">
     <path d="M 706.661788 78.813 
L 714.661788 78.813 
L 722.661788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_50">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(729.061788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 329.92182 287.64765 
L 340.71286 287.64765 
L 340.71286 71.82685 
L 329.92182 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image6b3b5c16eb" transform="scale(1 -1) translate(0 -215.52)" x="329.76" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_19">
     <g id="line2d_80">
      <defs>
       <path id="m6077622cb4" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m6077622cb4" x="340.71286" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.0000 -->
      <g transform="translate(347.71286 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
       <use xlink:href="#DejaVuSans-30" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_81">
      <g>
       <use xlink:href="#m6077622cb4" x="340.71286" y="258.078752" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.0025 -->
      <g transform="translate(347.71286 261.498049) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-32" x="222.65625"/>
       <use xlink:href="#DejaVuSans-35" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_82">
      <g>
       <use xlink:href="#m6077622cb4" x="340.71286" y="228.509854" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.0050 -->
      <g transform="translate(347.71286 231.929151) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
       <use xlink:href="#DejaVuSans-30" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_83">
      <g>
       <use xlink:href="#m6077622cb4" x="340.71286" y="198.940956" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.0075 -->
      <g transform="translate(347.71286 202.360253) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-37" x="222.65625"/>
       <use xlink:href="#DejaVuSans-35" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_84">
      <g>
       <use xlink:href="#m6077622cb4" x="340.71286" y="169.372058" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 0.0100 -->
      <g transform="translate(347.71286 172.791355) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
       <use xlink:href="#DejaVuSans-30" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_85">
      <g>
       <use xlink:href="#m6077622cb4" x="340.71286" y="139.80316" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 0.0125 -->
      <g transform="translate(347.71286 143.222456) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-32" x="222.65625"/>
       <use xlink:href="#DejaVuSans-35" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_86">
      <g>
       <use xlink:href="#m6077622cb4" x="340.71286" y="110.234262" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_57">
      <!-- 0.0150 -->
      <g transform="translate(347.71286 113.653558) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-35" x="222.65625"/>
       <use xlink:href="#DejaVuSans-30" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="ytick_26">
     <g id="line2d_87">
      <g>
       <use xlink:href="#m6077622cb4" x="340.71286" y="80.665363" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_58">
      <!-- 0.0175 -->
      <g transform="translate(347.71286 84.08466) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-37" x="222.65625"/>
       <use xlink:href="#DejaVuSans-35" x="286.279297"/>
      </g>
     </g>
    </g>
    <g id="text_59">
     <!-- Neural Activation -->
     <g transform="translate(385.846141 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 329.92182 287.64765 
L 335.31734 287.64765 
L 340.71286 287.64765 
L 340.71286 71.82685 
L 335.31734 71.82685 
L 329.92182 71.82685 
L 329.92182 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pd23d8e79b6">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pcaebd83d46">
   <rect x="466.931015" y="44.84925" width="360.638273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
